#!/usr/bin/env node

/**
 * Translation validation script
 * Checks for missing keys between Japanese and English translations
 * Run with: node scripts/validate-translations.js
 */

const fs = require('fs')
const path = require('path')

// Import the messages (this will trigger TypeScript validation if run through tsc)
const messagesPath = path.join(__dirname, '../src/language/messages.js')

function validateTranslations() {
  console.log('🔍 Validating translation keys...\n')

  try {
    // Read the messages file
    const messagesContent = fs.readFileSync(messagesPath, 'utf8')
    
    // Extract the messages object (simplified parsing)
    // In a real implementation, you might want to use a proper JS parser
    const jaMatch = messagesContent.match(/ja:\s*{([\s\S]*?)},\s*en:/s)
    const enMatch = messagesContent.match(/en:\s*{([\s\S]*?)}\s*}\)/s)
    
    if (!jaMatch || !enMatch) {
      console.error('❌ Could not parse messages file')
      process.exit(1)
    }

    // Extract keys from both languages
    const jaKeys = extractKeys(jaMatch[1])
    const enKeys = extractKeys(enMatch[1])

    console.log(`📊 Found ${jaKeys.size} Japanese keys`)
    console.log(`📊 Found ${enKeys.size} English keys\n`)

    // Find missing keys
    const missingInEn = [...jaKeys].filter(key => !enKeys.has(key))
    const missingInJa = [...enKeys].filter(key => !jaKeys.has(key))

    let hasErrors = false

    if (missingInEn.length > 0) {
      console.error('❌ Keys missing in English translations:')
      missingInEn.forEach(key => console.error(`   - ${key}`))
      console.error('')
      hasErrors = true
    }

    if (missingInJa.length > 0) {
      console.error('❌ Keys missing in Japanese translations:')
      missingInJa.forEach(key => console.error(`   - ${key}`))
      console.error('')
      hasErrors = true
    }

    if (!hasErrors) {
      console.log('✅ All translation keys are consistent between languages!')
      console.log('✅ TypeScript type checking will catch any future mismatches.')
    } else {
      console.error('💡 Fix these missing keys to ensure type safety.')
      process.exit(1)
    }

  } catch (error) {
    console.error('❌ Error validating translations:', error.message)
    process.exit(1)
  }
}

function extractKeys(content) {
  const keys = new Set()
  
  // Match key patterns like: KEY_NAME: 'value',
  const keyRegex = /^\s*([A-Z_][A-Z0-9_]*)\s*:/gm
  let match
  
  while ((match = keyRegex.exec(content)) !== null) {
    const key = match[1]
    // Skip special keys
    if (key !== '$vuetify' && !key.startsWith('//')) {
      keys.add(key)
    }
  }
  
  return keys
}

// Run validation
if (require.main === module) {
  validateTranslations()
}

module.exports = { validateTranslations }
