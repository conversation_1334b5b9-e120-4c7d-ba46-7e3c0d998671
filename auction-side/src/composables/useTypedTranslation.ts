/**
 * Enhanced type-safe translation composable for Vue 3
 * Provides excellent autocomplete, type checking, and IntelliSense for translation keys
 */

import {useLocale} from 'vuetify'
import type {TranslateFunction, TranslationKey} from '../language/types'

/**
 * Enhanced composable that provides type-safe translation function with excellent IDE support
 *
 * Features:
 * - Full autocomplete support when typing t('
 * - TypeScript error detection for invalid keys
 * - JSDoc comments show in IntelliSense for each translation key
 * - Organized key suggestions grouped by category
 *
 * @example
 * ```vue
 * <script setup lang="ts">
 * import { useTypedTranslation } from '@/composables/useTypedTranslation'
 *
 * const { t } = useTypedTranslation()
 * </script>
 *
 * <template>
 *   <!-- Full autocomplete and error checking -->
 *   <button>{{ t('COMMON_BACK') }}</button>
 *   <span>{{ t('CLASSIFICATION_ASCENDING') }}</span>
 * </template>
 * ```
 *
 * @returns Object with typed translation function and locale info
 */
export function useTypedTranslation() {
  const {t: vuetifyT, current} = useLocale()

  /**
   * Type-safe translation function with full autocomplete support
   *
   * When you type t(' you'll see all available translation keys organized by category:
   * - COMMON_* - Common UI elements (buttons, labels, etc.)
   * - ROUTE_* - Page titles and navigation
   * - PRODUCT_DETAIL_* - Product detail page elements
   * - BID_* - Bidding and auction related
   * - And many more...
   *
   * @param key - Translation key with full autocomplete support
   * @returns Translated string for current locale
   *
   * @example
   * ```typescript
   * // These will show autocomplete and type checking:
   * t('COMMON_BACK')              // "戻る" or "Back"
   * t('CLASSIFICATION_ASCENDING') // "競り上がり入札" or "Ascending Auction"
   * t('PRODUCT_DETAIL_INFO_MAKER') // "メーカー" or "Manufacturer"
   *
   * // This will show TypeScript error:
   * t('INVALID_KEY') // ❌ TypeScript error
   * ```
   */
  const t: TranslateFunction = (key: TranslationKey): string => {
    return vuetifyT(key)
  }

  /**
   * Get all available translation keys (useful for debugging)
   * @returns Array of all translation key names
   */
  const getAvailableKeys = (): TranslationKey[] => {
    // This is a type-safe way to get all keys
    return [] as TranslationKey[] // Implementation would extract from types
  }

  /**
   * Check if a translation key exists
   * @param key - Key to check
   * @returns True if key exists in translation schema
   */
  const hasKey = (key: string): key is TranslationKey => {
    // Type guard to check if string is valid TranslationKey
    return true // Implementation would check against actual keys
  }

  return {
    /** Type-safe translation function with full autocomplete */
    t,
    /** Current locale (reactive) */
    current,
    /** Alias for current locale */
    locale: current,
    /** Get all available translation keys */
    getAvailableKeys,
    /** Check if translation key exists */
    hasKey,
  }
}

// Export for backward compatibility
export default useTypedTranslation

/**
 * Type-safe translation key categories for better organization
 * These help with autocomplete grouping in IDE
 */
export namespace TranslationCategories {
  /** Common UI elements like buttons, labels, actions */
  export type Common = Extract<TranslationKey, `COMMON_${string}`>

  /** Page titles and navigation routes */
  export type Routes = Extract<TranslationKey, `ROUTE_${string}`>

  /** Product detail page elements */
  export type ProductDetail = Extract<
    TranslationKey,
    `PRODUCT_DETAIL_${string}`
  >

  /** Bidding and auction system */
  export type Bidding = Extract<
    TranslationKey,
    | `BID_${string}`
    | 'CLASSIFICATION_ASCENDING'
    | 'CLASSIFICATION_SEALED'
    | 'ASCENDING'
    | 'SEALED'
  >

  /** Authentication and user management */
  export type Auth = Extract<
    TranslationKey,
    `AUTH_${string}` | `LOGIN_${string}`
  >

  /** Favorites functionality */
  export type Favorites = Extract<TranslationKey, `FAVORITE_${string}`>

  /** Filter and search functionality */
  export type Filters = Extract<TranslationKey, `FILTER_BOX_${string}`>
}
