import {en as enLocale, ja as jaLocale} from 'vuetify/locale'
import {validateMessages} from './types'

export const messages = validateMessages({
  ja: {
    $vuetify: {
      ...jaLocale,
      dataIterator: {
        rowsPerPageText: '表示件数:',
        pageText: '{0}-{1} of {2}',
      },
    },

    // Common translations
    COMMON_BACK: '戻る',
    COMMON_BACK_LIST: '一覧へ戻る',
    COMMON_MORE: 'もっと見る',
    COMMON_JAPAN: '日本',
    COMMON_DUBAI: 'ドバイ',
    COMMON_HONGKONG: '香港',
    COMMON_MITAIOU: '未設定',
    COMMON_SEND: '送信する',
    COMMON_AGREE: '同意する',
    COMMON_ERROR: 'エラーが発生しました。',
    COMMON_CONFIRM: '確認',
    COMMON_INPUT_ERROR: '入力エラー',
    COMMON_DATE_FORMAT: 'yyyy年MM月dd日',
    COMMON_REMOVE: '削除',
    COMMON_UPDATE_AUCTION: '更新',
    COMMON_DAY: '日',
    COMMON_HOUR: '時間',
    COMMON_MINUTE: '分',
    COMMON_SECOND: '秒',

    // Site info
    SITE_TITLE: 'WORLDMOBILE Auction',
    COPYRIGHT:
      'Copyright ©2011-株式会社ワールドモバイルWORLDMOBILE All Rights Reserved.',

    // Top page app bar
    TOP_APP_BAR_SELECT_CATEGORY: 'カテゴリを選択',
    TOP_APP_BAR_REGISTER: '新規登録',
    TOP_APP_BAR_LOGIN: 'ログイン',
    TOP_APP_BAR_LOGOUT: 'ログアウト',

    // Routes
    ROUTE_TOP: 'トップ',
    ROUTE_LOGIN: 'ログイン',
    ROUTE_REMINDER: 'パスワード忘れ',
    ROUTE_REGISTER: '会員登録申請',
    ROUTE_NOTICE_LIST: 'お知らせ一覧',
    ROUTE_IMPORTANT_NOTICE_LIST: '重要なお知らせ一覧',
    ROUTE_IMPORTANT_NOTICE: '重要なお知らせ',
    ROUTE_BID_HISTORY_ALL: 'オークション落札結果',
    ROUTE_DETAILS: '商品詳細',
    ROUTE_CONTACT: 'お問い合わせ',
    ROUTE_CONTACT_CONFIRM: 'お問い合わせ確認',
    ROUTE_NOTICE_DETAILS: 'お知らせ',
    ROUTE_FAVORITES: 'お気に入り',
    ROUTE_BID_ONGOING: '入札中',
    ROUTE_BID_HISTORY: '落札履歴',
    ROUTE_MY_PAGE: 'マイページ',
    ROUTE_MY_PAGE_EDIT_CONFIRM: '会員編集確認',
    ROUTE_COMPANY_OVERVIEW: '会社概要',
    ROUTE_TERMS: '利用規約',
    ROUTE_PRIVACY: 'プライバシーポリシー',
    ROUTE_FIRST_TIME: '初めての方へ',
    ROUTE_TOSHUHO: '特定商取引法に基づく表記',

    // Product detail
    PRODUCT_DETAIL_TITLE: '商品詳細',
    PRODUCT_DETAIL_CURRENCY: '$',
    PRODUCT_DETAIL_QUANTITY: '出品数量',
    PRODUCT_DETAIL_LOWEST_BID_QUANTITY: '最低入札数量',
    PRODUCT_DETAIL_LOWEST_BID_PRICE: '最低入札単価',
    PRODUCT_DETAIL_BID_COUNT: '入札件数',
    PRODUCT_DETAIL_BID_QUANTITY: '入札数量',
    PRODUCT_DETAIL_BID_UNIT_PRICE: '入札単価',
    PRODUCT_DETAIL_BID_PRICE_FOR_ASC_AUCTION: '入札価格',
    PRODUCT_DETAIL_BID_TOTAL_PRICE: '入札合計金額',
    PRODUCT_DETAIL_BID_BUTTON: '入札する',
    PRODUCT_DETAIL_CONTACT_BUTTON: 'この商品に関するお問い合わせ',
    PRODUCT_DETAIL_ABOUT_RANK: '商品状態ランクについて',

    // Product detail info
    PRODUCT_DETAIL_INFO_MAKER: 'メーカー',
    PRODUCT_DETAIL_INFO_PRODUCT_NAME: '商品名',
    PRODUCT_DETAIL_INFO_SIM: 'SIM',
    PRODUCT_DETAIL_INFO_CAPACITY: '容量',
    PRODUCT_DETAIL_INFO_COLOR: '色',
    PRODUCT_DETAIL_INFO_RANK: 'グレード',
    PRODUCT_DETAIL_INFO_QUANTITY: '数量',
    PRODUCT_DETAIL_INFO_NOTE1: '備考1',
    PRODUCT_DETAIL_INFO_NOTE2: '備考2',
    PRODUCT_DETAIL_INFO_LOWEST_BID_PRICE: '最低入札<br>単価',
    PRODUCT_DETAIL_INFO_LOWEST_BID_QUANTITY: '最低入札<br>数量',
    PRODUCT_DETAIL_INFO_FAVORITE: 'お気に入り',
    PRODUCT_DETAIL_INFO_START_PRICE: '開始価格',
    PRODUCT_DETAIL_INFO_CURRENT_PRICE: '現在価格',

    // Filter box
    FILTER_BOX_TITLE: '検索条件',
    FILTER_BOX_INPUT_PLACEHOLDER: 'メーカー・商品名を入力',
    FILTER_BOX_KEYWORD: 'キーワード',
    FILTER_BOX_SEARCH_BUTTON: 'この条件で検索する',
    FILTER_BOX_CATEGORY: 'カテゴリ',
    FILTER_BOX_CLEAR_CONDITIONS: '条件をクリア',
    FILTER_BOX_AUCTION_COUNT: '件のオークション',
    FILTER_BOX_SEARCH_CRITERIA: '検索条件',

    // Favorite
    FAVORITE_TITLE: 'お気に入り',
    FAVORITE_EMPTY: 'お気に入りに商品がありません。',
    FAVORITE_CLEAR_PRICE_INPUT: '入力値クリア',
    FAVORITE_SUB_TOTAL_BID_PRICE_HEADER: '入札小計',
    FAVORITE_SUB_TOTAL_BID_PRICE: '入札小計',
    FAVORITE_LOGIN_REQUIRED_FAVORITE:
      'ログイン後にお気に入りリストに入れられます。',
    FAVORITE_BID_BUTTON: '入札する',
    FAVORITE_RE_BID_BUTTON: '再入札する',
    FAVORITE_BID_QUANTITY: '入札数量',
    FAVORITE_DELETE_FAVORITE1: 'お気に入り',
    FAVORITE_DELETE_FAVORITE2: 'から削除',

    // Bid history
    BID_HISTORY_END_DATE: '落札日',
    BID_HISTORY_BID_SUCCESS_UNIT_PRICE: '落札単価',
    BID_HISTORY_BID_SUCCESS_PRICE: '落札価格',
    BID_HISTORY_BID_SUCCESS_QUANTITY: '落札数',
    BID_HISTORY_BID_TOTAL_PRICE: '合計金額',

    // Auth
    AUTH_LOGOUT_MESSAGE: 'ログアウトしますか？',
    AUTH_LOGOUT: 'ログアウト',
    AUTH_CLOSE: '閉じる',
    AUTH_CANCEL: 'キャンセル',

    // Login
    LOGIN_TITLE: 'ログイン',
    LOGIN_EMAIL: 'メールアドレス',
    LOGIN_PASSWORD: 'パスワード',
    LOGIN_SAVE_LOGIN_INFO: 'メールアドレス・パスワードを保存',
    LOGIN_FORGET_PASSWORD: 'パスワードを忘れた方はコチラ',
    LOGIN_RULE: '参加規約',
    LOGIN_AGREE_RULE: '参加規約に同意する',
    LOGIN_AGREE: '同意する',
    LOGIN_ENTRY_INFO1: '新規会員登録(無料)',
    LOGIN_ENTRY_INFO2: '商品への入札は会員登録が必要です。',
    LOGIN_CONFIRM_BUTTON: 'ログイン',
    LOGIN_PASSWORD_HINT: '8～16文字の半角英数字',

    // Existing uppercase keys (keep as-is)
    COMMON_BID_LABEL: '入札',
    BID_COUNT: '件',
    CLASSIFICATION_ASCENDING: '競り上がり入札',
    CLASSIFICATION_SEALED: '封印入札',
    ASCENDING: '競り上がり入札',
    SEALED: '封印入札',
    BID_STATUS_INPROGRESS: '入札受付中',
    BID_STATUS_CANCEL: '出品停止',
    BID_STATUS_NOT_START_YET: '開始待ち',
    BID_STATUS_ENDED: '終了',
    BID_STATUS_EXTENDING: '延長中',
    HIGHEST_BIDDER: '最高入札者',
    BID_STATUS: 'ステータス',
    REMAINING_TIME: '残り時間',
    ANATA_TOP: 'あなたがTOP',
    RESERVE_PRICE_NOT_MET: '最低落札価格に達していません',
    RESERVE_PRICE_EXCEEDED: '最低落札超え',
    MORE_LITTLE: 'あと少し',
    SECOND_BIDDER: '2位入札者',
    END_DATE_TIME: '終了日時',
    START_DATE_TIME: '開始日時',
    RECORDED_BID_PRICE: '入札済み価格',
  },

  en: {
    $vuetify: {
      ...enLocale,
      dataIterator: {
        rowsPerPageText: 'Number of items displayed:',
        pageText: '{0}-{1} of {2}',
      },
    },

    // Common translations
    COMMON_BACK: 'Back',
    COMMON_BACK_LIST: 'Back to List',
    COMMON_MORE: 'Show More',
    COMMON_JAPAN: 'Japan',
    COMMON_DUBAI: 'Dubai',
    COMMON_HONGKONG: 'Hong Kong',
    COMMON_MITAIOU: 'Unset',
    COMMON_SEND: 'Send',
    COMMON_AGREE: 'Agree',
    COMMON_ERROR: 'Error occurred.',
    COMMON_CONFIRM: 'Confirm',
    COMMON_INPUT_ERROR: 'Input Error',
    COMMON_DATE_FORMAT: 'yyyy/MM/dd',
    COMMON_REMOVE: 'Remove',
    COMMON_UPDATE_AUCTION: 'Update',
    COMMON_DAY: 'd',
    COMMON_HOUR: 'h',
    COMMON_MINUTE: 'm',
    COMMON_SECOND: 's',

    // Site info
    SITE_TITLE: 'WORLDMOBILE Auction',
    COPYRIGHT: 'Copyright ©2011-WORLDMOBILE All Rights Reserved.',

    // Top page app bar
    TOP_APP_BAR_SELECT_CATEGORY: 'Select Category',
    TOP_APP_BAR_REGISTER: 'New Member Registration',
    TOP_APP_BAR_LOGIN: 'Sign In',
    TOP_APP_BAR_LOGOUT: 'Sign Out',

    // Routes
    ROUTE_TOP: 'TOP',
    ROUTE_LOGIN: 'Sign In',
    ROUTE_REMINDER: 'Forgot Password',
    ROUTE_REGISTER: 'Membership Request',
    ROUTE_NOTICE_LIST: 'Notice List',
    ROUTE_IMPORTANT_NOTICE_LIST: 'Important Notices',
    ROUTE_IMPORTANT_NOTICE: 'Important Notice',
    ROUTE_BID_HISTORY_ALL: 'Auction Results',
    ROUTE_DETAILS: 'Product Details',
    ROUTE_CONTACT: 'Inquiry',
    ROUTE_CONTACT_CONFIRM: 'Inquiry Confirmation',
    ROUTE_NOTICE_DETAILS: 'Notification',
    ROUTE_FAVORITES: 'My favorites',
    ROUTE_BID_ONGOING: 'Bidding',
    ROUTE_BID_HISTORY: 'Bid History',
    ROUTE_MY_PAGE_EDIT_CONFIRM: 'Member Edit Confirmation',
    ROUTE_COMPANY_OVERVIEW: 'Company Profile',
    ROUTE_TERMS: 'Terms of Service',
    ROUTE_PRIVACY: 'Privacy Policy',
    ROUTE_FIRST_TIME: 'For First Time Users',
    ROUTE_TOSHUHO: 'Specified Commercial Transactions Law',

    // Product detail
    PRODUCT_DETAIL_TITLE: 'Product Details',
    PRODUCT_DETAIL_CURRENCY: '$',
    PRODUCT_DETAIL_QUANTITY: 'Quantity',
    PRODUCT_DETAIL_LOWEST_BID_QUANTITY: 'Minimum bid quantity',
    PRODUCT_DETAIL_LOWEST_BID_PRICE: 'Minimum Bid Unit Price',
    PRODUCT_DETAIL_BID_COUNT: 'Number of bids',
    PRODUCT_DETAIL_BID_QUANTITY: 'Bid Quantity',
    PRODUCT_DETAIL_BID_UNIT_PRICE: 'Bid Unit Price',
    PRODUCT_DETAIL_BID_PRICE_FOR_ASC_AUCTION: 'Bid Price',
    PRODUCT_DETAIL_BID_TOTAL_PRICE: 'Total Bid Amount',
    PRODUCT_DETAIL_BID_BUTTON: 'Place Bid',
    PRODUCT_DETAIL_CONTACT_BUTTON: 'Inquiries about this product',
    PRODUCT_DETAIL_ABOUT_RANK: 'Product Grade Details',

    // Product detail info
    PRODUCT_DETAIL_INFO_MAKER: 'Manufacturer',
    PRODUCT_DETAIL_INFO_PRODUCT_NAME: 'Product Name',
    PRODUCT_DETAIL_INFO_SIM: 'SIM',
    PRODUCT_DETAIL_INFO_CAPACITY: 'Capacity',
    PRODUCT_DETAIL_INFO_COLOR: 'Color',
    PRODUCT_DETAIL_INFO_RANK: 'Grade',
    PRODUCT_DETAIL_INFO_QUANTITY: 'Quantity',
    PRODUCT_DETAIL_INFO_NOTE1: 'Note 1',
    PRODUCT_DETAIL_INFO_NOTE2: 'Note 2',
    PRODUCT_DETAIL_INFO_LOWEST_BID_PRICE: 'Minimum Bid Price',
    PRODUCT_DETAIL_INFO_LOWEST_BID_QUANTITY: 'Minimum Bid Quantity',
    PRODUCT_DETAIL_INFO_FAVORITE: 'Favorite',
    PRODUCT_DETAIL_INFO_START_PRICE: 'Starting Price',
    PRODUCT_DETAIL_INFO_CURRENT_PRICE: 'Current Price',

    // Filter box
    FILTER_BOX_TITLE: 'Advanced Search',
    FILTER_BOX_INPUT_PLACEHOLDER: 'Enter manufacturer or product name',
    FILTER_BOX_KEYWORD: 'Keyword',
    FILTER_BOX_SEARCH_BUTTON: 'Search with these conditions',
    FILTER_BOX_CATEGORY: 'Category',
    FILTER_BOX_CLEAR_CONDITIONS: 'Clear the conditions',
    FILTER_BOX_AUCTION_COUNT: 'auctions',
    FILTER_BOX_SEARCH_CRITERIA: 'Search condition',

    // Favorite
    FAVORITE_TITLE: 'Favorite',
    FAVORITE_EMPTY: 'Your favorites list is currently empty.',
    FAVORITE_CLEAR_PRICE_INPUT: 'Clear Input',
    FAVORITE_SUB_TOTAL_BID_PRICE_HEADER: 'Bidding<br>Subtotal',
    FAVORITE_SUB_TOTAL_BID_PRICE: 'Bidding Subtotal',
    FAVORITE_LOGIN_REQUIRED_FAVORITE:
      'You can put the item in your favorites list after signing in.',
    FAVORITE_BID_BUTTON: 'Place a bid',
    FAVORITE_RE_BID_BUTTON: 'Rebid',
    FAVORITE_BID_QUANTITY: 'Bid Quantity',
    FAVORITE_DELETE_FAVORITE1: 'Remove from',
    FAVORITE_DELETE_FAVORITE2: 'Favorites',

    // Bid history
    BID_HISTORY_END_DATE: 'End Date',
    BID_HISTORY_BID_SUCCESS_UNIT_PRICE: 'Winning Price',
    BID_HISTORY_BID_SUCCESS_PRICE: 'Winning Price',
    BID_HISTORY_BID_SUCCESS_QUANTITY: 'Winning Qty',
    BID_HISTORY_BID_TOTAL_PRICE: 'Total Amount',

    // Auth
    AUTH_LOGOUT_MESSAGE: 'Would you like to sign out?',
    AUTH_LOGOUT: 'Sign out',
    AUTH_CLOSE: 'Close',
    AUTH_CANCEL: 'Cancel',

    // Login
    LOGIN_TITLE: 'Sign in',
    LOGIN_EMAIL: 'Email Address',
    LOGIN_PASSWORD: 'Password',
    LOGIN_SAVE_LOGIN_INFO: 'Save email address and password',
    LOGIN_FORGET_PASSWORD: 'Forgot Password?',
    LOGIN_RULE: 'Terms of Service',
    LOGIN_AGREE_RULE: 'I agree to the Terms of Service',
    LOGIN_AGREE: 'Agree',
    LOGIN_ENTRY_INFO1: 'New Member Registration (Free)',
    LOGIN_ENTRY_INFO2: 'Registration is required to bid on items.',
    LOGIN_CONFIRM_BUTTON: 'Sign in',
    LOGIN_PASSWORD_HINT: '8-16 alphanumeric characters',

    // Existing uppercase keys (keep as-is)
    COMMON_BID_LABEL: 'Bid',
    BID_COUNT: '',
    CLASSIFICATION_ASCENDING: 'Ascending Auction',
    CLASSIFICATION_SEALED: 'Sealed Auction',
    ASCENDING: 'Ascending',
    SEALED: 'Sealed',
    BID_STATUS_INPROGRESS: 'In Progress',
    BID_STATUS_CANCEL: 'Suspension of exhibits',
    BID_STATUS_NOT_START_YET: 'Waiting to Start',
    BID_STATUS_ENDED: 'End',
    BID_STATUS_EXTENDING: 'Extending',
    HIGHEST_BIDDER: 'Highest Bidder',
    BID_STATUS: 'Status',
    REMAINING_TIME: 'Remaining time',
    ANATA_TOP: 'Top bidder',
    RESERVE_PRICE_NOT_MET: 'The minimum bid price has not been reached.',
    RESERVE_PRICE_EXCEEDED: 'Exceeding the minimum required bid',
    MORE_LITTLE: 'Just A little more',
    SECOND_BIDDER: '2nd place bidder',
    END_DATE_TIME: 'End date and time',
    START_DATE_TIME: 'Start date and time',
    RECORDED_BID_PRICE: 'Your Price:',
  },
})
