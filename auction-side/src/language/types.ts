/**
 * TypeScript type definitions for i18n translation keys with enhanced IDE support
 * This ensures both ja and en objects have exactly the same keys and provides
 * excellent autocomplete and IntelliSense experience
 */

export interface TranslationKeys {
  $vuetify: {
    dataIterator: {
      rowsPerPageText: string
      pageText: string
    }
    [key: string]: any
  }

  // ==========================================
  // COMMON TRANSLATIONS
  // ==========================================

  /** Navigation: Back button text */
  COMMON_BACK: string

  /** Navigation: Back to list button text */
  COMMON_BACK_LIST: string

  /** Navigation: Show more items button text */
  COMMON_MORE: string

  /** Location: Japan country name */
  COMMON_JAPAN: string

  /** Location: Dubai country name */
  COMMON_DUBAI: string

  /** Location: Hong Kong country name */
  COMMON_HONGKONG: string

  /** Status: Unset/Not configured status */
  COMMON_MITAIOU: string

  /** Action: Send/Submit button text */
  COMMON_SEND: string

  /** Action: Agree/Accept button text */
  COMMON_AGREE: string

  /** Error: General error message */
  COMMON_ERROR: string

  /** Action: Confirm button text */
  COMMON_CONFIRM: string

  /** Error: Input validation error message */
  COMMON_INPUT_ERROR: string

  /** Format: Date display format */
  COMMON_DATE_FORMAT: string

  /** Action: Remove/Delete button text */
  COMMON_REMOVE: string

  /** Action: Update auction button text */
  COMMON_UPDATE_AUCTION: string

  /** Time: Day unit label */
  COMMON_DAY: string

  /** Time: Hour unit label */
  COMMON_HOUR: string

  /** Time: Minute unit label */
  COMMON_MINUTE: string

  /** Time: Second unit label */
  COMMON_SECOND: string

  // ==========================================
  // SITE INFORMATION
  // ==========================================

  /** Site: Main application title */
  SITE_TITLE: string

  /** Site: Copyright notice text */
  COPYRIGHT: string

  // ==========================================
  // TOP PAGE APP BAR
  // ==========================================

  /** AppBar: Category selection dropdown text */
  TOP_APP_BAR_SELECT_CATEGORY: string

  /** AppBar: New member registration link text */
  TOP_APP_BAR_REGISTER: string

  /** AppBar: Login link text */
  TOP_APP_BAR_LOGIN: string

  /** AppBar: Logout link text */
  TOP_APP_BAR_LOGOUT: string

  // ==========================================
  // ROUTE/PAGE TITLES
  // ==========================================

  /** Route: Top/Home page title */
  ROUTE_TOP: string

  /** Route: Login page title */
  ROUTE_LOGIN: string

  /** Route: Password reminder page title */
  ROUTE_REMINDER: string

  /** Route: Member registration page title */
  ROUTE_REGISTER: string

  /** Route: Notice list page title */
  ROUTE_NOTICE_LIST: string

  /** Route: Important notice list page title */
  ROUTE_IMPORTANT_NOTICE_LIST: string

  /** Route: Important notice detail page title */
  ROUTE_IMPORTANT_NOTICE: string

  /** Route: Bid history (all auctions) page title */
  ROUTE_BID_HISTORY_ALL: string

  /** Route: Product detail page title */
  ROUTE_DETAILS: string

  /** Route: Contact/Inquiry page title */
  ROUTE_CONTACT: string

  /** Route: Contact confirmation page title */
  ROUTE_CONTACT_CONFIRM: string

  /** Route: Notice details page title */
  ROUTE_NOTICE_DETAILS: string

  /** Route: Favorites page title */
  ROUTE_FAVORITES: string

  /** Route: Ongoing bids page title */
  ROUTE_BID_ONGOING: string

  /** Route: Bid history page title */
  ROUTE_BID_HISTORY: string

  /** Route: My page title */
  ROUTE_MY_PAGE: string

  /** Route: My page edit confirmation title */
  ROUTE_MY_PAGE_EDIT_CONFIRM: string

  /** Route: Company overview page title */
  ROUTE_COMPANY_OVERVIEW: string

  /** Route: Terms of service page title */
  ROUTE_TERMS: string

  /** Route: Privacy policy page title */
  ROUTE_PRIVACY: string

  /** Route: First time user guide page title */
  ROUTE_FIRST_TIME: string

  /** Route: Commercial transactions law page title */
  ROUTE_TOSHUHO: string

  // ==========================================
  // PRODUCT DETAIL PAGE
  // ==========================================

  /** ProductDetail: Page title */
  PRODUCT_DETAIL_TITLE: string

  /** ProductDetail: Currency symbol for prices */
  PRODUCT_DETAIL_CURRENCY: string

  /** ProductDetail: Available quantity label */
  PRODUCT_DETAIL_QUANTITY: string

  /** ProductDetail: Minimum bid quantity label */
  PRODUCT_DETAIL_LOWEST_BID_QUANTITY: string

  /** ProductDetail: Minimum bid price label */
  PRODUCT_DETAIL_LOWEST_BID_PRICE: string

  /** ProductDetail: Number of bids label */
  PRODUCT_DETAIL_BID_COUNT: string

  /** ProductDetail: Bid quantity input label */
  PRODUCT_DETAIL_BID_QUANTITY: string

  /** ProductDetail: Bid unit price input label */
  PRODUCT_DETAIL_BID_UNIT_PRICE: string

  /** ProductDetail: Bid price for ascending auction */
  PRODUCT_DETAIL_BID_PRICE_FOR_ASC_AUCTION: string

  /** ProductDetail: Total bid amount label */
  PRODUCT_DETAIL_BID_TOTAL_PRICE: string

  /** ProductDetail: Place bid button text */
  PRODUCT_DETAIL_BID_BUTTON: string

  /** ProductDetail: Contact about product button text */
  PRODUCT_DETAIL_CONTACT_BUTTON: string

  /** ProductDetail: About product rank link text */
  PRODUCT_DETAIL_ABOUT_RANK: string

  // ==========================================
  // PRODUCT DETAIL INFO SECTION
  // ==========================================

  /** ProductInfo: Manufacturer/Brand label */
  PRODUCT_DETAIL_INFO_MAKER: string

  /** ProductInfo: Product name label */
  PRODUCT_DETAIL_INFO_PRODUCT_NAME: string

  /** ProductInfo: SIM card type label */
  PRODUCT_DETAIL_INFO_SIM: string

  /** ProductInfo: Storage capacity label */
  PRODUCT_DETAIL_INFO_CAPACITY: string

  /** ProductInfo: Product color label */
  PRODUCT_DETAIL_INFO_COLOR: string

  /** ProductInfo: Product condition rank label */
  PRODUCT_DETAIL_INFO_RANK: string

  /** ProductInfo: Available quantity label */
  PRODUCT_DETAIL_INFO_QUANTITY: string

  /** ProductInfo: Additional note 1 label */
  PRODUCT_DETAIL_INFO_NOTE1: string

  /** ProductInfo: Additional note 2 label */
  PRODUCT_DETAIL_INFO_NOTE2: string

  /** ProductInfo: Minimum bid price label */
  PRODUCT_DETAIL_INFO_LOWEST_BID_PRICE: string

  /** ProductInfo: Minimum bid quantity label */
  PRODUCT_DETAIL_INFO_LOWEST_BID_QUANTITY: string

  /** ProductInfo: Add to favorites label */
  PRODUCT_DETAIL_INFO_FAVORITE: string

  /** ProductInfo: Starting price label */
  PRODUCT_DETAIL_INFO_START_PRICE: string

  /** ProductInfo: Current price label */
  PRODUCT_DETAIL_INFO_CURRENT_PRICE: string

  // Filter box
  FILTER_BOX_TITLE: string
  FILTER_BOX_INPUT_PLACEHOLDER: string
  FILTER_BOX_KEYWORD: string
  FILTER_BOX_SEARCH_BUTTON: string
  FILTER_BOX_CATEGORY: string
  FILTER_BOX_CLEAR_CONDITIONS: string
  FILTER_BOX_AUCTION_COUNT: string
  FILTER_BOX_SEARCH_CRITERIA: string

  // Favorite
  FAVORITE_TITLE: string
  FAVORITE_EMPTY: string
  FAVORITE_CLEAR_PRICE_INPUT: string
  FAVORITE_SUB_TOTAL_BID_PRICE_HEADER: string
  FAVORITE_SUB_TOTAL_BID_PRICE: string
  FAVORITE_LOGIN_REQUIRED_FAVORITE: string
  FAVORITE_BID_BUTTON: string
  FAVORITE_RE_BID_BUTTON: string
  FAVORITE_BID_QUANTITY: string
  FAVORITE_DELETE_FAVORITE1: string
  FAVORITE_DELETE_FAVORITE2: string

  // Bid history
  BID_HISTORY_END_DATE: string
  BID_HISTORY_BID_SUCCESS_UNIT_PRICE: string
  BID_HISTORY_BID_SUCCESS_PRICE: string
  BID_HISTORY_BID_SUCCESS_QUANTITY: string
  BID_HISTORY_BID_TOTAL_PRICE: string

  // Auth
  AUTH_LOGOUT_MESSAGE: string
  AUTH_LOGOUT: string
  AUTH_CLOSE: string
  AUTH_CANCEL: string

  // Login
  LOGIN_TITLE: string
  LOGIN_EMAIL: string
  LOGIN_PASSWORD: string
  LOGIN_SAVE_LOGIN_INFO: string
  LOGIN_FORGET_PASSWORD: string
  LOGIN_RULE: string
  LOGIN_AGREE_RULE: string
  LOGIN_AGREE: string
  LOGIN_ENTRY_INFO1: string
  LOGIN_ENTRY_INFO2: string
  LOGIN_CONFIRM_BUTTON: string
  LOGIN_PASSWORD_HINT: string

  // ==========================================
  // AUCTION & BIDDING SYSTEM
  // ==========================================

  /** Bidding: General bid label */
  COMMON_BID_LABEL: string

  /** Bidding: Count suffix (e.g., "2 bids") */
  BID_COUNT: string

  /** Auction: Ascending auction type label - competitive bidding */
  CLASSIFICATION_ASCENDING: string

  /** Auction: Sealed auction type label - blind bidding */
  CLASSIFICATION_SEALED: string

  /** Auction: Short form of ascending auction */
  ASCENDING: string

  /** Auction: Short form of sealed auction */
  SEALED: string

  /** BidStatus: Auction is accepting bids */
  BID_STATUS_INPROGRESS: string

  /** BidStatus: Auction has been cancelled/stopped */
  BID_STATUS_CANCEL: string

  /** BidStatus: Auction hasn't started yet */
  BID_STATUS_NOT_START_YET: string

  /** BidStatus: Auction has ended */
  BID_STATUS_ENDED: string

  /** BidStatus: Auction time is being extended */
  BID_STATUS_EXTENDING: string

  /** Bidding: Label for highest bidder */
  HIGHEST_BIDDER: string

  /** Bidding: Current bid status label */
  BID_STATUS: string

  /** Bidding: Time remaining in auction */
  REMAINING_TIME: string

  /** Bidding: You are the top bidder message */
  ANATA_TOP: string

  /** Bidding: Reserve price not met warning */
  RESERVE_PRICE_NOT_MET: string

  /** Bidding: Reserve price exceeded message */
  RESERVE_PRICE_EXCEEDED: string

  /** Bidding: Almost there message */
  MORE_LITTLE: string

  /** Bidding: Second place bidder label */
  SECOND_BIDDER: string

  /** Auction: End date and time label */
  END_DATE_TIME: string

  /** Auction: Start date and time label */
  START_DATE_TIME: string

  /** Bidding: Your recorded bid price */
  RECORDED_BID_PRICE: string
}

export interface Messages {
  ja: TranslationKeys
  en: TranslationKeys
}

// Extract all translation keys as a union type for type-safe t() function
export type TranslationKey = keyof Omit<TranslationKeys, '$vuetify'>

// Type for the t() function
export type TranslateFunction = (key: TranslationKey) => string

// Utility type to ensure both languages have the same keys
export type EnsureKeysMatch<T extends Record<string, TranslationKeys>> = {
  [K in keyof T]: TranslationKeys
} & T

// Type guard to ensure messages object has matching keys
export function validateMessages<T extends Record<string, TranslationKeys>>(
  messages: EnsureKeysMatch<T>
): EnsureKeysMatch<T> {
  return messages
}
