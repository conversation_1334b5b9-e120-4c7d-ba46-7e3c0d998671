<script setup lang="ts">
import { useTypedTranslation } from '@/composables/useTypedTranslation'

// This provides full type safety and autocomplete
const { t } = useTypedTranslation()

// Try typing t(' and you should see autocomplete with all available keys
// Try using an invalid key like t('INVALID_KEY') and you'll see a TypeScript error
</script>

<template>
  <div class="demo-container">
    <h2>Type-Safe Translation Demo</h2>
    
    <!-- These keys have full autocomplete and type checking -->
    <p>{{ t('COMMON_BACK') }}</p>
    <p>{{ t('COMMON_SEND') }}</p>
    <p>{{ t('ROUTE_TOP') }}</p>
    <p>{{ t('PRODUCT_DETAIL_INFO_MAKER') }}</p>
    <p>{{ t('CLASSIFICATION_ASCENDING') }}</p>
    
    <!-- Try uncommenting this line to see TypeScript error -->
    <!-- <p>{{ t('INVALID_KEY') }}</p> -->
  </div>
</template>

<style scoped>
.demo-container {
  padding: 20px;
  border: 1px solid #ccc;
  margin: 20px;
}
</style>
