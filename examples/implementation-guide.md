# i18n Refactoring Implementation Guide

## Overview

This guide shows how to refactor your Vue 3 i18n setup from separate language files with nested keys to a single combined file with uppercase flat keys.

## 1. New Combined Messages File Structure

The new `auction-side/src/language/messages.js` file combines both languages in a single structure:

```javascript
import {ja as jaLocale} from 'vuetify/locale'
import {en as enLocale} from 'vuetify/locale'

export const messages = {
  ja: {
    $vuetify: { ...jaLocale },
    
    // Converted from nested to flat uppercase keys
    COMMON_BACK: '戻る',                    // was: common.back
    COMMON_SEND: '送信する',                // was: common.send
    ROUTE_TOP: 'トップ',                   // was: route.top
    ROUTE_LOGIN: 'ログイン',               // was: route.login
    PRODUCT_DETAIL_INFO_MAKER: 'メーカー',  // was: productDetail.info.maker
    
    // Existing uppercase keys remain unchanged
    CLASSIFICATION_ASCENDING: '競り上がり入札',
    CLASSIFICATION_SEALED: '封印入札',
    BID_STATUS: 'ステータス',
  },
  
  en: {
    $vuetify: { ...enLocale },
    
    // Same structure for English
    COMMON_BACK: 'Back',
    COMMON_SEND: 'Send', 
    ROUTE_TOP: 'TOP',
    ROUTE_LOGIN: 'Sign In',
    PRODUCT_DETAIL_INFO_MAKER: 'Manufacturer',
    
    // Existing uppercase keys remain unchanged
    CLASSIFICATION_ASCENDING: 'Ascending Auction',
    CLASSIFICATION_SEALED: 'Sealed Auction',
    BID_STATUS: 'Status',
  }
}
```

## 2. Updated Index File

The `auction-side/src/language/index.js` file is simplified:

```javascript
import {createI18n} from 'vue-i18n'
import {messages} from './messages'

const i18n = createI18n({
  legacy: false,
  warnHtmlMessage: false,
  locale: 'ja',
  fallbackLocale: 'en',
  messages,
})

export default i18n
```

## 3. Component Migration Examples

### Example A: Simple Key Updates

**Before:**
```vue
<template>
  <button>{{ t('common.back') }}</button>
  <span>{{ t('common.send') }}</span>
</template>
```

**After:**
```vue
<template>
  <button>{{ t('COMMON_BACK') }}</button>
  <span>{{ t('COMMON_SEND') }}</span>
</template>
```

### Example B: Product Detail Keys

**Before:**
```vue
<template>
  <th>{{ t('productDetail.info.maker') }}</th>
  <th>{{ t('productDetail.info.productName') }}</th>
  <th>{{ t('productDetail.info.sim') }}</th>
</template>
```

**After:**
```vue
<template>
  <th>{{ t('PRODUCT_DETAIL_INFO_MAKER') }}</th>
  <th>{{ t('PRODUCT_DETAIL_INFO_PRODUCT_NAME') }}</th>
  <th>{{ t('PRODUCT_DETAIL_INFO_SIM') }}</th>
</template>
```

### Example C: No Changes Needed (Already Uppercase)

Components using existing uppercase keys remain unchanged:

```vue
<template>
  <!-- These stay exactly the same -->
  <span>{{ t('CLASSIFICATION_ASCENDING') }}</span>
  <span>{{ t('CLASSIFICATION_SEALED') }}</span>
  <span>{{ t('BID_STATUS') }}</span>
</template>
```

## 4. Key Conversion Mapping

| Category | Old Nested Key | New Uppercase Key |
|----------|----------------|-------------------|
| Common | `common.back` | `COMMON_BACK` |
| Common | `common.send` | `COMMON_SEND` |
| Common | `common.more` | `COMMON_MORE` |
| Routes | `route.top` | `ROUTE_TOP` |
| Routes | `route.login` | `ROUTE_LOGIN` |
| Routes | `route.details` | `ROUTE_DETAILS` |
| Product | `productDetail.info.maker` | `PRODUCT_DETAIL_INFO_MAKER` |
| Product | `productDetail.info.productName` | `PRODUCT_DETAIL_INFO_PRODUCT_NAME` |
| Product | `productDetail.info.sim` | `PRODUCT_DETAIL_INFO_SIM` |
| Filter | `filterBox.auctionCount` | `FILTER_BOX_AUCTION_COUNT` |
| Favorite | `favorite.deleteFavorite1` | `FAVORITE_DELETE_FAVORITE1` |
| Bid History | `bidHistory.endDate` | `BID_HISTORY_END_DATE` |

## 5. Implementation Steps

1. **Create the new messages.js file** with the combined structure
2. **Update index.js** to import from the new file
3. **Update all Vue components** to use uppercase keys
4. **Update router meta.name values** to use uppercase keys
5. **Test thoroughly** to ensure all translations work
6. **Remove old en.js and ja.js files** after verification

## 6. Benefits

- ✅ **Single source of truth**: All translations in one file
- ✅ **Consistent naming**: All keys follow uppercase convention  
- ✅ **Prevents mismatches**: No risk of missing keys between languages
- ✅ **Better maintainability**: Easier to add/modify translations
- ✅ **IDE support**: Better autocomplete and find/replace functionality
- ✅ **Backward compatibility**: Existing uppercase keys work unchanged

## 7. Testing Your ClassificationSwitch Component

Your existing component should work without any changes:

```vue
<span v-if="!isMobile">{{ t('CLASSIFICATION_ASCENDING') }}</span>
```

This will continue to work exactly as before because `CLASSIFICATION_ASCENDING` was already an uppercase key in both the old and new structure.

## 8. Migration Checklist

- [ ] Create new `messages.js` file with combined structure
- [ ] Update `index.js` to use new messages
- [ ] Update all `t('common.*')` calls to `t('COMMON_*')`
- [ ] Update all `t('route.*')` calls to `t('ROUTE_*')`
- [ ] Update all `t('productDetail.*')` calls to `t('PRODUCT_DETAIL_*')`
- [ ] Update router meta.name values
- [ ] Test all components with both languages
- [ ] Verify ClassificationSwitch still works
- [ ] Remove old en.js and ja.js files
