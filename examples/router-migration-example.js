// Example showing how router meta.name values would change

// BEFORE (using nested keys):
const routesBefore = [
  {
    path: PATH_NAME.TOP,
    name: 'top',
    component: TopView,
    meta: {
      name: 'route.top',  // OLD nested key
      bodyId: 'top',
    },
  },
  {
    path: PATH_NAME.LOGIN,
    name: 'login',
    component: () => import('@/components/login/LoginPage.vue'),
    meta: {
      name: 'route.login',  // OLD nested key
      bodyId: 'login',
    },
  },
  {
    path: PATH_NAME.ENTRY_INFO_CONFIRM,
    name: 'entry-info-complete',
    component: () => import('@/components/register/RegisterPage.vue'),
    meta: {
      isConfirm: true,
      name: 'route.register',  // OLD nested key
      bodyId: 'entry',
    },
  },
  {
    path: `${PATH_NAME.DETAIL}/:manageNo`,
    name: 'details',
    component: ProductDetail,
    meta: {
      name: 'route.details',  // OLD nested key
      bodyClass: 'stock',
      bodyId: 'detail',
    },
  },
]

// AFTER (using uppercase keys):
const routesAfter = [
  {
    path: PATH_NAME.TOP,
    name: 'top',
    component: TopView,
    meta: {
      name: 'ROUTE_TOP',  // NEW uppercase key
      bodyId: 'top',
    },
  },
  {
    path: PATH_NAME.LOGIN,
    name: 'login',
    component: () => import('@/components/login/LoginPage.vue'),
    meta: {
      name: 'ROUTE_LOGIN',  // NEW uppercase key
      bodyId: 'login',
    },
  },
  {
    path: PATH_NAME.ENTRY_INFO_CONFIRM,
    name: 'entry-info-complete',
    component: () => import('@/components/register/RegisterPage.vue'),
    meta: {
      isConfirm: true,
      name: 'ROUTE_REGISTER',  // NEW uppercase key
      bodyId: 'entry',
    },
  },
  {
    path: `${PATH_NAME.DETAIL}/:manageNo`,
    name: 'details',
    component: ProductDetail,
    meta: {
      name: 'ROUTE_DETAILS',  // NEW uppercase key
      bodyClass: 'stock',
      bodyId: 'detail',
    },
  },
]
