# Vue Component Migration Examples

## Example 1: BreadCrumb Component

### Before (using nested keys):
```vue
<script setup>
  import {computed} from 'vue'
  import {useRoute} from 'vue-router'
  import {useLocale} from 'vuetify'
  import {PATH_NAME} from '../../defined/const'

  const props = defineProps({
    customTitle: {
      type: String,
      default: null,
    },
  })

  const route = useRoute()
  const {t: translate} = useLocale()

  const breadcrumbTitle = computed(() => {
    return props.customTitle || translate(route.meta.name)
  })
</script>
<template>
  <div id="pNav">
    <ul>
      <li><router-link :to="PATH_NAME.TOP">TOP</router-link></li>
      <li>{{ breadcrumbTitle }}</li>
    </ul>
  </div>
</template>
```

### After (using uppercase keys):
```vue
<script setup>
  import {computed} from 'vue'
  import {useRoute} from 'vue-router'
  import {useLocale} from 'vuetify'
  import {PATH_NAME} from '../../defined/const'

  const props = defineProps({
    customTitle: {
      type: String,
      default: null,
    },
  })

  const route = useRoute()
  const {t: translate} = useLocale()

  const breadcrumbTitle = computed(() => {
    return props.customTitle || translate(route.meta.name)
  })
</script>
<template>
  <div id="pNav">
    <ul>
      <li><router-link :to="PATH_NAME.TOP">{{ translate('ROUTE_TOP') }}</router-link></li>
      <li>{{ breadcrumbTitle }}</li>
    </ul>
  </div>
</template>
```

## Example 2: Account ConfirmData Component

### Before (using nested keys):
```vue
<template>
  <form>
    <!-- form content -->
    <div class="btn-form">
      <input
        class="btn-back"
        type="button"
        :value="t('common.back')"
        @click="emit('back')"
      />
      <input
        type="button"
        :value="t('common.send')"
        @click="emit('registMember', registMember, loading.requestSend)"
      />
    </div>
  </form>
</template>
```

### After (using uppercase keys):
```vue
<template>
  <form>
    <!-- form content -->
    <div class="btn-form">
      <input
        class="btn-back"
        type="button"
        :value="t('COMMON_BACK')"
        @click="emit('back')"
      />
      <input
        type="button"
        :value="t('COMMON_SEND')"
        @click="emit('registMember', registMember, loading.requestSend)"
      />
    </div>
  </form>
</template>
```

## Example 3: Product Detail Info Component

### Before (using nested keys):
```vue
<script setup>
  import {useLocale} from 'vuetify'

  defineProps(['freeField'])
  const {t} = useLocale()
</script>
<template>
  <div class="contents-wrap">
    <table>
      <tbody>
        <tr class="spec">
          <th>{{ t('productDetail.info.maker') }}</th>
          <td>{{ freeField?.maker }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('productDetail.info.productName') }}</th>
          <td>{{ freeField?.product_name }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('productDetail.info.sim') }}</th>
          <td>{{ freeField?.sim }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('productDetail.info.capacity') }}</th>
          <td>{{ freeField?.capacity }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('productDetail.info.color') }}</th>
          <td>{{ freeField?.color }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
```

### After (using uppercase keys):
```vue
<script setup>
  import {useLocale} from 'vuetify'

  defineProps(['freeField'])
  const {t} = useLocale()
</script>
<template>
  <div class="contents-wrap">
    <table>
      <tbody>
        <tr class="spec">
          <th>{{ t('PRODUCT_DETAIL_INFO_MAKER') }}</th>
          <td>{{ freeField?.maker }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('PRODUCT_DETAIL_INFO_PRODUCT_NAME') }}</th>
          <td>{{ freeField?.product_name }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('PRODUCT_DETAIL_INFO_SIM') }}</th>
          <td>{{ freeField?.sim }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('PRODUCT_DETAIL_INFO_CAPACITY') }}</th>
          <td>{{ freeField?.capacity }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('PRODUCT_DETAIL_INFO_COLOR') }}</th>
          <td>{{ freeField?.color }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
```

## Example 4: ClassificationSwitch Component (No Changes Needed)

This component already uses uppercase keys, so it remains unchanged:

```vue
<script setup>
  import {computed} from 'vue'
  import {useLocale} from 'vuetify'
  import {CLASSIFICATIONS} from '../../defined/const'
  import {useSearchResultStore} from '../../stores/search-results'
  import {isMobile} from '../../utils'

  const searchResultStore = useSearchResultStore()
  const {t} = useLocale()

  const isAscendingActive = computed(
    () =>
      searchResultStore.myPageSelectedClassification ===
      CLASSIFICATIONS.ASCENDING
  )

  const emit = defineEmits(['click:changeClassification'])
</script>

<template>
  <section id="method-switch">
    <div class="container">
      <div class="nav-wrap">
        <a
          @click="emit('click:changeClassification', CLASSIFICATIONS.SEALED)"
          :class="{'nav-content-active': !isAscendingActive}"
        >
          <span v-if="!isMobile">{{ t('CLASSIFICATION_SEALED') }}</span>
          <span v-else>{{ t('SEALED') }}</span>
        </a>
        <a
          @click="emit('click:changeClassification', CLASSIFICATIONS.ASCENDING)"
          :class="{'nav-content-active': isAscendingActive}"
        >
          <span v-if="!isMobile">{{ t('CLASSIFICATION_ASCENDING') }}</span>
          <span v-else>{{ t('ASCENDING') }}</span>
        </a>
      </div>
    </div>
  </section>
</template>
```

## Key Conversion Reference

| Old Nested Key | New Uppercase Key |
|----------------|-------------------|
| `route.top` | `ROUTE_TOP` |
| `route.login` | `ROUTE_LOGIN` |
| `common.back` | `COMMON_BACK` |
| `common.send` | `COMMON_SEND` |
| `common.more` | `COMMON_MORE` |
| `productDetail.info.maker` | `PRODUCT_DETAIL_INFO_MAKER` |
| `productDetail.info.productName` | `PRODUCT_DETAIL_INFO_PRODUCT_NAME` |
| `productDetail.info.sim` | `PRODUCT_DETAIL_INFO_SIM` |
| `filterBox.auctionCount` | `FILTER_BOX_AUCTION_COUNT` |
| `favorite.deleteFavorite1` | `FAVORITE_DELETE_FAVORITE1` |
| `bidHistory.endDate` | `BID_HISTORY_END_DATE` |

## Existing Uppercase Keys (No Changes)

These keys already follow the uppercase convention and remain unchanged:
- `CLASSIFICATION_ASCENDING`
- `CLASSIFICATION_SEALED` 
- `ASCENDING`
- `SEALED`
- `BID_STATUS`
- `HIGHEST_BIDDER`
- `REMAINING_TIME`
- `COMMON_BID_LABEL`
- `BID_COUNT`
