<template>
  <CCardBody>
    <CForm onsubmit="return false;">
      <CRow class="mb-3" v-for="field in fieldList">
        <CCol sm="2">
          <label>{{ field.logical_name }}</label>
        </CCol>
        <CCol sm="4">
          <CFormSelect
            v-if="field.input_type == 'pulldown'"
            :name="field.physical_name"
            :options="constants[field.input_data_list.key_string]"
            v-model="memberEditData.freeField[field.physical_name]"
            disabled
          />
          <label
            v-else-if="
              field.input_type == 'file' &&
              memberEditData.freeField[field.physical_name]
            "
            class="word-break"
          >
            <span v-for="file in memberEditData.freeField[field.physical_name]"
              >{{ file }}　</span
            >
          </label>
          <label v-else class="word-break">{{
            memberEditData.freeField[field.physical_name]
          }}</label>
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2">
          <label>入札</label>
        </CCol>
        <CCol sm="2">
          <label class="word-break">{{
            memberEditData.bidAllowFlag === '1' ? '可' : '否'
          }}</label>
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2">
          <label>入札上限額</label>
        </CCol>
        <CCol sm="2">
          <label class="word-break"
            >{{ memberEditData.bidLimitAmount }} 円</label
          >
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2">
          <label>メール配信</label>
        </CCol>
        <CCol sm="2">
          <label class="word-break">{{
            memberEditData.emailDeliveryFlag === '1' ? '配信する' : '配信しない'
          }}</label>
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2"> 備考 </CCol>
        <CCol sm="8">
          <p
            style="white-space: pre-line"
            v-html="memberEditData.freeField.memo"
          ></p>
        </CCol>
      </CRow>
    </CForm>
  </CCardBody>
</template>

<script setup>
  import Methods from '@/api/methods';
  import {dateFormat} from '@/views/common/commonFilters';
  import {defineProps, onMounted, ref} from 'vue';

  const props = defineProps({
    countryOptions: {
      type: Array,
      default: () => [],
    },
    memberEditData: {
      type: Object,
      default: () => ({
        freeField: {},
      }),
    },
    emailLangOptions: {
      type: Array,
      default: () => [],
    },
  });
  let fieldList = ref([]);
  let constants = ref({});
  onMounted(async () => {
    const response = await Methods.apiExecute('get-field-list', {
      language_code: 'ja',
      resource_type: 'member',
    });
    fieldList.value = response.data.data;
    const constantsRes = await Methods.apiExecute('get-constants-by-keys', {
      key_strings: fieldList.value
        .filter(row => row.input_data_list && row.input_data_list.key_string)
        .map(row => row.input_data_list.key_string),
    });
    constantsRes.data.forEach(element => {
      if (!constants.value[element.key_string])
        constants.value[element.key_string] = [
          {
            value: null,
            label: '',
          },
        ];
      constants.value[element.key_string].push({
        value: element.value1,
        label: element.value2,
      });
    });
    console.log(`memberEditData = ${JSON.stringify(props.memberEditData)}`);
  });

  const getCountryName = country => {
    const label = props.countryOptions.find(
      item => item.value === country
    )?.label;
    return label || '';
  };

  const getEmailLangName = emailLang => {
    const lang = props.emailLangOptions.find(
      item => item.value === emailLang
    )?.title;
    return lang || '';
  };
</script>
<style type="text/css">
  .word-break {
    word-break: break-all;
  }
</style>
