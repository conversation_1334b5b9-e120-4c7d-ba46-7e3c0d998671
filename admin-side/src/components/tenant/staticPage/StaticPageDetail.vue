<template>
  <CCard class="mb-3">
    <CCardHeader class="form-inline">
      <label
        ><strong
          >静的ページ{{ route.params.id ? '編集' : '登録' }}</strong
        ></label
      >
    </CCardHeader>
    <CCardBody>
      <CRow class="mb-3">
        <CCol sm="2" class="d-flex align-items-center">
          <label>パース</label>
          <CBadge color="danger" class="ms-auto">必須</CBadge>
        </CCol>
        <CCol sm="5">
          <CFormInput
            ref="path"
            type="text"
            placeholder="例：profile"
            horizontal
            v-model="pagePath"
            :disabled="isBtnDisabled"
            :invalid="!!errorMsg.page_path"
            :feedback-invalid="errorMsg.page_path"
            @change="errorMsg.page_path = null"
          />
        </CCol>
      </CRow>
    </CCardBody>

    <div v-for="lang in languages" :key="lang.code">
      <CCardHeader
        style="
          border-top: 1px solid;
          border-bottom: none;
          border-color: #d8dbe0;
          border-radius: 0;
        "
      >
        <strong>{{ lang.name }}</strong>
      </CCardHeader>
      <CCardBody>
        <CRow class="mb-3">
          <CCol sm="2" class="d-flex align-items-center">
            <label>ページ名</label>
            <CBadge color="danger" class="ms-auto">必須</CBadge>
          </CCol>
          <CCol sm="9">
            <CFormInput
              type="text"
              v-model="pageName[lang.code]"
              :disabled="isBtnDisabled"
              :invalid="!!errorMsg[`page_name_${lang.code}`]"
              :feedback-invalid="errorMsg[`page_name_${lang.code}`]"
              @change="errorMsg[`page_name_${lang.code}`] = null"
            />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="2">
            <label>テンプレート</label>
          </CCol>
          <CCol sm="9">
            <div v-if="pageTemplateFile[lang.code]" class="mb-2">
              <a
                class="text-link cursor-pointer"
                @click="getFileDownloadUrl(pageTemplateFile[lang.code])"
                >{{
                  pageTemplateFile[lang.code].split('/').pop().toLowerCase()
                }}</a
              >
            </div>
            <div v-if="!!errorMsg[`file_url_${lang.code}`]" class="text-danger">
              {{ errorMsg[`file_url_${lang.code}`] }}
            </div>
            <label
              name="btn-select"
              class="btn btn-block"
              :class="isBtnDisabled ? 'btn-disabled' : 'btn-secondary'"
            >
              参照...
              <input
                :disabled="isBtnDisabled"
                type="file"
                ref="fileupload"
                multiple
                data-max="10"
                @change="
                  e => {
                    errorMsg[`file_url_${lang.code}`] = null;
                    onFileCompChange(e, lang.code);
                  }
                "
                hidden
              />
            </label>
            <!-- <CFormInput
              type="file"
              @change="
                e => {
                  errorMsg[`file_url_${lang.code}`] = null
                  onFileCompChange(e, lang.code)
                }
              "
              :disabled="isBtnDisabled"
              :invalid="!!errorMsg[`file_url_${lang.code}`]"
              :feedback-invalid="errorMsg[`file_url_${lang.code}`]"
            /> -->
          </CCol>
        </CRow>
        <CElementCover v-if="loading" :opacity="0.8" />
      </CCardBody>
    </div>
    <CCardFooter>
      <CButton class="mx-1" color="secondary" @click="goBack"
        >登録を中止して一覧に戻る</CButton
      >
      <CButton
        class="mx-1"
        color="primary"
        @click="openRegistModal"
        :disabled="isBtnDisabled"
        :class="isBtnDisabled ? 'pt-not-allow' : ''"
        >{{ route.params.id ? '更新' : '登録' }}する</CButton
      >
      <CButton
        color="danger"
        class="float-right"
        @click="openDeleteModal"
        v-if="route.params.id"
        :disabled="isBtnDisabled"
        :class="isBtnDisabled ? 'pt-not-allow' : ''"
        >削除</CButton
      >
      <CElementCover v-if="loading" :opacity="0.8" />
    </CCardFooter>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="registModal"
      @close="
        () => {
          registModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>{{ registModalTitle }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!loading && validateResult.length === 0">
          この内容で{{
            route.params.id ? '更新' : '登録'
          }}してもよろしいですか？
        </div>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loading"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="registModal = false"
          color="dark"
          :disabled="loading"
          v-if="validateResult.length === 0"
          >キャンセル
        </CButton>
        <CButton @click="btnRegistClicked" color="primary" :disabled="loading"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="errorModal"
      @close="
        () => {
          errorModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>入力エラー</CModalTitle>
      </CModalHeader>
      <CModalBody> データの取得が失敗しました！ </CModalBody>
      <CModalFooter>
        <CButton @click="errorModal = false" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="compModal"
      @close="
        () => {
          compModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>処理が完了しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="compModal = false" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="cancelModal"
      @close="
        () => {
          cancelModal = false;
          btn_clicked = false;
          nextRoute(false);
        }
      "
    >
      <CModalHeader>
        <CModalTitle>{{
          (route.params.id ? '編集' : '登録') + '中止確認'
        }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>入力内容は破棄されますがよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            (cancelModal = false), (btn_clicked = false), nextRoute(false)
          "
          color="dark"
          >キャンセル</CButton
        >
        <CButton
          @click="
            () => {
              cancelModal = false;
              nextRoute();
            }
          "
          color="danger"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="deleteModal"
      @close="
        () => {
          deleteModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>削除確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!loadingDelete && validateResult.length === 0">
          ページを削除してもよろしいですか？
        </div>

        <div style="margin-left: 10px">
          <scale-loader
            :loading="loadingDelete"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter v-if="validateResult.length === 0">
        <CButton
          @click="deleteModal = false"
          color="dark"
          :disabled="loadingDelete"
          >キャンセル</CButton
        >
        <CButton @click="deletePage" color="primary" :disabled="loadingDelete"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>
  </CCard>
</template>

<script setup>
  import Methods from '@/api/methods';
  import Upload from '@/api/uploadFileToS3';
  import Base from '@/common/base';
  import useFileDownload from '@/common/useFileDownload';
  import {CElementCover, ScaleLoader} from '@/components/Table';
  import cloneDeep from 'lodash-es/cloneDeep';
  import {computed, onMounted, ref} from 'vue';
  import {onBeforeRouteLeave, useRoute, useRouter} from 'vue-router';

  const router = useRouter();
  const route = useRoute();
  const {download} = useFileDownload();

  // Spinner
  const loading = ref(false);
  const errorMsg = ref({});
  const btn_clicked = ref(false);

  const origData = ref(null);
  const notice = ref(null);
  const languages = ref([]);
  const registModal = ref(false);
  const compModal = ref(false);
  const errorModal = ref(false);
  const changeFlag = ref(false);
  const cancelModal = ref(false);

  const pagePath = ref(null);
  const pageName = ref({});
  const pageTemplateFile = ref({});

  const validateResult = ref([]);
  const registModalTitle = ref('確認');
  const deleteModal = ref(false);
  const loadingDelete = ref(false);

  onBeforeRouteLeave((to, from, next) => {
    if (changeFlag.value) {
      cancelModal.value = true;
      nextRoute.value = next;
    } else {
      next();
    }
  });

  const isBtnDisabled = computed(() => {
    return loading.value;
  });

  const getConstantsData = async () => {
    loading.value = true;
    const request = {
      key_strings: ['LANGUAGE_CODE'],
    };
    // Request to server
    return Methods.apiExecute('get-constants-by-keys', request).then(
      response => {
        if (response.status === 200) {
          languages.value = [];
          for (const constant of response.data) {
            switch (constant.key_string) {
              case 'LANGUAGE_CODE':
                languages.value.push({
                  code: constant.value1,
                  name: constant.value2,
                });
                break;
              default:
                break;
            }
          }
        }
        return Promise.resolve();
      }
    );
  };

  const showScreen = () => {
    if (notice.value) {
      // Prepare data for each language
      pagePath.value = notice.value.page_path;
      pageName.value = {};
      pageTemplateFile.value = {};
      for (const i in languages.value) {
        const lang = languages.value[i];
        const noti = notice.value.localized.find(
          x => x.language_code === lang.code
        );
        if (noti) {
          pageName.value[lang.code] = noti.page_name;
          pageTemplateFile.value[lang.code] = noti.file_url;
        } else {
          pageName.value[lang.code] = '';
          pageTemplateFile.value[lang.code] = '';
        }
      }
    } else {
      // Prepare data for each language
      for (const i in languages.value) {
        const lang = languages.value[i];
        console.log(`lang = ${lang}`);
        if (route.params.id) {
          console.log('route.params.id');
        }
        pagePath.value = null;
        pageName.value[lang.code] = '';
        pageTemplateFile.value[lang.code] = '';
      }
    }
  };

  const getDataFromServer = searchCondition => {
    // Request to server
    return Methods.apiExecute('get-static-page', searchCondition).then(
      response => {
        if (!response?.data) {
          return Promise.resolve();
        }
        notice.value = response.data[0];
        // Copy not by reference
        origData.value = cloneDeep(notice.value);
        showScreen();
        return Promise.resolve();
      }
    );
  };
  const getData = async () => {
    const id = route.params.id;
    // Original初期化
    origData.value = null;
    if (typeof id === 'undefined' || id === null) {
      loading.value = false;
      // Add new mode
      showScreen();
    } else {
      // Edit mode
      await getDataFromServer({
        static_page_no: id,
      });
      loading.value = false;
    }
  };
  const goBack = () => {
    if (btn_clicked.value) {
      return;
    }
    btn_clicked.value = true;
    router.push({path: '/tenant/static-page'});
  };

  const openRegistModal = () => {
    registModalTitle.value = '確認';
    compModal.value = false;
    registModal.value = true;
    loading.value = false;
    validateResult.value = [];
  };
  const registNotice = () => {
    // Get data from screen
    const localized = languages.value.map(lang => ({
      language_code: lang.code,
      page_name: pageName.value[lang.code],
      file_url: pageTemplateFile.value[lang.code],
    }));
    const params = {
      static_page_no: notice.value?.static_page_no || 0,
      page_path: pagePath.value,
      localized,
    };
    const apiName = route.params.id ? 'edit-static-page' : 'add-static-page';
    return Methods.apiExecute(apiName, params)
      .then(response => {
        registModal.value = false;
        if (response.status === 200) {
          loading.value = false;
          // CompModal.value = true
          changeFlag.value = false;
          router.push({path: '/tenant/static-page'});
        }
        return Promise.resolve();
      })
      .catch(error => {
        loading.value = false;
        registModal.value = false;
        errorMsg.value = error.response?.data?.errors || {};
        return Promise.resolve();
      });
  };
  const onFileCompChange = async (e, langCode) => {
    changeFlag.value = true;
    const files = e.target.files;
    if (files.length === 0) {
      pageTemplateFile.value[langCode] = null;
      return;
    }
    const file = files[0];
    const fileName = file.name;
    const fileSize = file.size;
    const fileType = file.type;
    const fileExt = fileName.split('.').pop().toLowerCase();
    const maxSize = 1024 * 1024 * 5; // 5MB
    const allowedTypes = [
      'text/html',
      'application/xhtml+xml',
      'image/png',
      'image/jpeg',
    ];
    const allowedExts = ['html', 'htm', 'png', 'jpg', 'jpeg'];
    if (fileSize > maxSize) {
      errorMsg.value[`file_url_${langCode}`] =
        'ファイルサイズは5MB以下にしてください。';
      pageTemplateFile.value[langCode] = null;
      return;
    }
    if (!allowedTypes.includes(fileType) || !allowedExts.includes(fileExt)) {
      errorMsg.value[`file_url_${langCode}`] =
        'HTMLファイルを選択してください。';
      pageTemplateFile.value[langCode] = null;
      return;
    }
    // Upload file to S3
    try {
      const uploadResult = await Upload.uploadPromise('static-page', file);
      // Assume uploadResult returns the file URL
      pageTemplateFile.value[langCode] = uploadResult.message;
    } catch (error) {
      console.log('uploadFiles error', error);
      errorMsg.value[`file_url_${langCode}`] =
        'ファイルのアップロードに失敗しました。';
      pageTemplateFile.value[langCode] = null;
    }
  };

  const btnRegistClicked = async () => {
    console.log('btnRegistClicked');
    changeFlag.value = false;
    if (validateResult.value.length > 0) {
      // Close modal
      loading.value = false;
      registModal.value = false;
    } else {
      // Open modal
      loading.value = true;
      // Regist notice to DB
      await registNotice();
    }
  };

  const openDeleteModal = () => {
    loadingDelete.value = false;
    deleteModal.value = true;
  };
  const deletePage = async () => {
    loadingDelete.value = true;
    validateResult.value = [];
    const pageNo = route.params.id;
    console.log(`deletePage: ${JSON.stringify(pageNo)}`);

    // Request to server
    await Methods.apiExecute('delete-static-page', {
      static_page_no: pageNo,
    })
      .then(response => {
        console.log(`response = ${JSON.stringify(response)}`);
        loadingDelete.value = false;
        deleteModal.value = false;
        compModal.value = true;
        /*
         * If (response.status === 200) {
         *   goBack()
         * }
         */
      })
      .catch(error => {
        console.log(error);
        loadingDelete.value = false;
        validateResult.value = Methods.parseHtmlResponseError(router, error);
      });
  };

  const getFileDownloadUrl = fileUrl => {
    console.log('getFileDownloadUrl');
    // Get download url from file server
    if (fileUrl !== null && fileUrl !== '') {
      Upload.getDownloadUrl(fileUrl).then(res => {
        console.log(`res: ${JSON.stringify(res)}`);
        download(res, Base.getFileName(fileUrl));
      });
    }
  };

  onMounted(async () => {
    console.log('mounted');
    try {
      await getConstantsData();
      await getData();
    } catch (error) {
      console.log(JSON.stringify(error));
      loading.value = false;
      Methods.parseHtmlResponseError(router, error);
    }
  });
</script>
<style scoped>
  .cursor-pointer {
    cursor: pointer;
  }
</style>
