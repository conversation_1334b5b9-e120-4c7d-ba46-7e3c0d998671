<script setup>
  import {useTenantOptions} from '@/composables/useTenantOptions';
  import {onMounted, ref, watch} from 'vue';

  const emit = defineEmits(['refresh']);
  const props = defineProps({
    options: {
      type: Object,
      default: () => ({}),
    },
    triggerSaveBtn: {
      type: Number,
      default: 0,
    },
  });

  const {saveTenantOptions} = useTenantOptions();

  const showConfirm = ref(false);
  const isLoading = ref(false);
  const saveError = ref('');
  const toasts = ref([]);

  const createToast = ({color, textColor, title, message}) => {
    toasts.value.push({
      color: color || 'info',
      textColor: textColor || 'white',
      title: title || '',
      content: message || '',
    });
  };

  const bidLimitOptions = [
    {value: 1, label: '現在価格より高い'},
    {value: 2, label: '自由'},
  ];
  const bidSuccessPriceOptions = [
    {value: 1, label: 'ファーストプライス'},
    {value: 2, label: 'セカンドプライス'},
  ];
  const winnerSelectOptions = [
    {value: 1, label: '自動'},
    {value: 2, label: '手動'},
  ];

  const inputOptions = ref({
    // Checkbox options
    sankaseigen_seri: false,
    sankaseigen_fuin: false,
    minimum_price_seri: false,
    minimum_price_fuin: false,
    instant_win_seri: false,
    instant_win_fuin: false,
    bid_cancel_seri: false,
    bid_cancel_fuin: false,
    extension_seri: false,
    extension_fuin: false,
    // Select options
    bid_limit_seri: 1,
    bid_limit_fuin: 2,
    bid_success_price_seri: 2,
    bid_success_price_fuin: 1,
    winner_select_seri: 1,
    winner_select_fuin: 1,
  });

  const doSaveOptions = async () => {
    isLoading.value = true;
    saveError.value = '';
    try {
      // Bid Options
      const checkBoxOptions = [
        'sankaseigen_seri',
        'sankaseigen_fuin',
        'minimum_price_seri',
        'minimum_price_fuin',
        'instant_win_seri',
        'instant_win_fuin',
        'bid_cancel_seri',
        'bid_cancel_fuin',
        'extension_seri',
        'extension_fuin',
      ];
      const bidOptions = {};
      if (inputOptions.value) {
        Object.keys(inputOptions.value).forEach(key => {
          // Convert checkbox values to 1 or 0
          if (checkBoxOptions.includes(key)) {
            bidOptions[key] = inputOptions.value[key] ? 1 : 0;
          } else {
            // For select options, directly assign the value
            bidOptions[key] = inputOptions.value[key];
          }
        });
      }
      await saveTenantOptions({bidOptions});
      showConfirm.value = false;
      createToast({
        color: 'success',
        textColor: 'white',
        title: '保存成功',
        message: '設定が正常に保存されました',
      });
      emit('refresh');
    } catch (error) {
      console.error('Error saving options:', error);
      saveError.value = '保存に失敗しました';
      createToast({
        color: 'danger',
        textColor: 'white',
        title: '保存失敗',
        message: '設定の保存に失敗しました',
      });
    } finally {
      isLoading.value = false;
    }
  };

  onMounted(() => {
    // Initialize inputOptions with props if provided
    if (props.options) {
      inputOptions.value = {
        ...inputOptions.value,
        ...props.options,
      };
    }
  });

  watch(
    () => props.options,
    newOptions => {
      if (newOptions) {
        inputOptions.value = {
          ...inputOptions.value,
          ...newOptions,
        };
      }
    },
    {deep: true}
  );

  watch(
    () => props.triggerSaveBtn,
    () => {
      if (props.triggerSaveBtn > 0) {
        showConfirm.value = true;
      }
    }
  );
</script>
<template>
  <div>
    <CToaster class="p-3" placement="top-end">
      <CToast
        v-for="(toast, index) in toasts"
        visible
        :key="index"
        :autohide="true"
        :delay="5000"
        :color="toast.color"
        class="align-items-center"
        :class="`text-${toast.textColor}`"
      >
        <div class="d-flex">
          <CToastBody>{{ toast.content }}</CToastBody>
          <CToastClose class="me-2 m-auto" white />
        </div>
      </CToast>
    </CToaster>

    <CRow>
      <CCol
        sm="4"
        class="p-3 border d-flex justify-content-center align-items-center"
        ><h5>オプション</h5></CCol
      >
      <CCol
        sm="4"
        class="p-3 border d-flex justify-content-center align-items-center"
        ><h5>競り上げ</h5></CCol
      >
      <CCol
        sm="4"
        class="p-3 border d-flex justify-content-center align-items-center"
        ><h5>封印</h5></CCol
      >
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">参加制限設定</CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck
          name="sankaseigen_seri"
          v-model="inputOptions.sankaseigen_seri"
      /></CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck
          name="sankaseigen_fuin"
          v-model="inputOptions.sankaseigen_fuin"
      /></CCol>
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">最低落札価格</CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck
          name="sankaseigen_seri"
          v-model="inputOptions.minimum_price_seri"
      /></CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck
          name="sankaseigen_fuin"
          v-model="inputOptions.minimum_price_fuin"
      /></CCol>
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">入札制限</CCol>
      <CCol sm="4" class="p-3 border">
        <CFormSelect
          name="bid_limit_seri"
          :options="bidLimitOptions"
          v-model="inputOptions.bid_limit_seri"
        />
      </CCol>
      <CCol sm="4" class="p-3 border">
        <CFormSelect
          name="bid_limit_fuin"
          :options="bidLimitOptions"
          v-model="inputOptions.bid_limit_fuin"
        />
      </CCol>
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">即決</CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck
          name="sankaseigen_seri"
          v-model="inputOptions.instant_win_seri"
      /></CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck
          name="sankaseigen_fuin"
          v-model="inputOptions.instant_win_fuin"
      /></CCol>
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">入札取消</CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck
          name="sankaseigen_seri"
          v-model="inputOptions.bid_cancel_seri"
      /></CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck
          name="sankaseigen_fuin"
          v-model="inputOptions.bid_cancel_fuin"
      /></CCol>
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">延長</CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck
          name="sankaseigen_seri"
          v-model="inputOptions.extension_seri"
      /></CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck
          name="sankaseigen_fuin"
          v-model="inputOptions.extension_fuin"
      /></CCol>
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">落札価格</CCol>
      <CCol sm="4" class="p-3 border">
        <CFormSelect
          name="bid_success_price_seri"
          :options="bidSuccessPriceOptions"
          v-model="inputOptions.bid_success_price_seri"
        />
      </CCol>
      <CCol sm="4" class="p-3 border">
        <CFormSelect
          name="bid_success_price_fuin"
          :options="bidSuccessPriceOptions"
          v-model="inputOptions.bid_success_price_fuin"
        />
      </CCol>
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">落札者決定</CCol>
      <CCol sm="4" class="p-3 border">
        <CFormSelect
          name="winner_select_seri"
          :options="winnerSelectOptions"
          v-model="inputOptions.winner_select_seri"
        />
      </CCol>
      <CCol sm="4" class="p-3 border">
        <CFormSelect
          name="winner_select_fuin"
          :options="winnerSelectOptions"
          v-model="inputOptions.winner_select_fuin"
        />
      </CCol>
    </CRow>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="showConfirm"
      @close="
        () => {
          showConfirm = false;
        }
      "
    >
      <CModalHeader>保存の確認</CModalHeader>
      <CModalBody>
        <div v-if="isLoading" class="text-center py-3">
          <span
            class="spinner-border spinner-border-sm"
            role="status"
            aria-hidden="true"
          ></span>
          <span class="ms-2">保存中...</span>
        </div>
        <div v-else>
          <div v-if="saveError" class="text-danger mb-2">{{ saveError }}</div>
          この内容で保存してもよろしいですか？
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          color="secondary"
          @click="showConfirm = false"
          :disabled="isLoading"
          >キャンセル</CButton
        >
        <CButton color="primary" @click="doSaveOptions" :disabled="isLoading"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>
