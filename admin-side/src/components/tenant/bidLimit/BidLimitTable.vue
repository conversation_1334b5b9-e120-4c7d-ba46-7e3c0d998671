<template>
  <CCard>
    <CCardHeader>
      <strong>設定履歴</strong>
    </CCardHeader>
    <CCardBody>
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :loading="loading"
        :items="items"
        :fields="fields"
      >
        <template #start_datetime="{item}">
          <td class="text-center">
            {{ formatDate(item.start_datetime) }}
          </td>
        </template>
        <template #end_datetime="{item}">
          <td class="text-center">
            {{ formatDate(item.end_datetime) }}
          </td>
        </template>
        <template #bid_limit="{item}">
          <td class="text-center">
            {{ formatNumber(item.bid_limit) }}
          </td>
        </template>
        <template #reset_type="{item}">
          <td class="text-center">
            {{ formatResetType(item.reset_type, item.reset_date) }}
          </td>
        </template>
      </CDataTable>
    </CCardBody>
  </CCard>
</template>

<script setup>
  import {CDataTable} from '@/components/Table';
  import {defineEmits, defineProps} from 'vue';

  const props = defineProps({
    items: Array,
    fields: {
      type: Array,
      default() {
        return [
          {
            key: 'start_datetime',
            label: '適用開始日',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'end_datetime',
            label: '適用終了日',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'bid_limit',
            label: '入札上限額(円)',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'reset_type',
            label: 'リセット型式',
            _classes: 'text-center',
            sorter: false,
          },
        ];
      },
    },
    caption: {
      type: String,
      default: 'postageTable',
    },
    loading: Boolean,
  });

  const resetTypeOptions = [
    {value: 'auction', label: '入札会ごとにリセット'},
    {value: 'period', label: '期間でリセット'},
  ];

  const formatDate = dateStr => {
    if (!dateStr) return '';
    const d = new Date(dateStr);
    return `${d.getFullYear()}/${String(d.getMonth() + 1).padStart(2, '0')}/${String(d.getDate()).padStart(2, '0')}`;
  };

  const formatNumber = num => {
    if (num === null || num === undefined) return '';
    return Number(num).toLocaleString();
  };

  const formatResetType = (id, date) => {
    if (id === 0) return '入札会ごとにリセット';
    if (id === 1)
      return `期間でリセット (${date === 0 ? '月末' : `毎月${date}日`})`;
    return '';
  };
</script>
