const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  ctx.callbackWaitsForEmptyEventLoop = false;
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => Validator.validation(params))
    .then(() => {
      const sqlParams = [
        Base.extractTenantId(e),
        Base.extractAdminLanguageCode(e),
        params.exhibitionNo,
      ];
      // 入札結果情報を取得する
      return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_EXHIBITION_RESULT_FUNCTION, sqlParams);
    })
    .then(result => {
      return Base.uploadCsvToS3(
        result.map(row => {
          // 入札合計金額
          const totalBidPrice =
            row.bid_price && row.bid_quantity
              ? Math.floor(row.bid_price * row.bid_quantity)
              : '';

          return {
            productId:
              row.manage_no && row.manage_no.length > 0 ? row.manage_no : '',
            productName: row.item_field.product_name,
            quantity: Base.number2string(row.quantity),
            lowestBidQuantity: Base.number2string(row.lowest_bid_quantity),
            lowestBidPrice: Base.number2string(
              row.lowest_bid_price ? Math.floor(row.lowest_bid_price) : ''
            ),
            lowestBidAcceptQuantity: Base.number2string(
              row.lowest_bid_accept_quantity
            ),
            lowestBidAcceptPrice: Base.number2string(
              row.lowest_bid_accept_price
                ? Math.floor(row.lowest_bid_accept_price)
                : ''
            ),
            bidPrice: Base.number2string(
              row.bid_price ? Math.floor(row.bid_price) : ''
            ),
            bidQuantity: Base.number2string(row.bid_quantity),
            totalBidPrice: Base.number2string(totalBidPrice),
            bidSuccessQuantity: Base.number2string(row.bid_success_quantity),
            taxRate: row.tax_rate ? `${row.tax_rate}%` : '',
            hummerFlag: row.hummer_flag === 1 ? '落札' : '流札',
            bidSuccessMemberId: row.bid_success_member_id,
            customerCode: row.member_field ? row.member_field.customerCode : '',
            companyName: row.member_field ? row.member_field.companyName : '',
          };
        }),
        Define.CSV.EXPORT.BID_RESULT_CSV
      );
    })
    .then(result => {
      const data = {
        url: result,
      };
      return Base.createSuccessResponse(cb, data);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
