const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.PGHOST);

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  const tenantNo = Base.extractTenantId(e);
  console.log(`params = ${JSON.stringify(params)}`);
  console.log('create-member');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      const sqlParams = [tenantNo, 'member', 'ja'];
      return pool
        .rlsQuery(tenantNo, Define.QUERY.GET_FIELD_LIST_FUNCTION, sqlParams)
      .then((result) => {
        console.log('result', result)
        const freeField = Object.assign({}, params.member.freeField);
        const errorList = []
        if (params.validation_mode === true) {
          result.forEach(field => {
            if (field.required_flag && !freeField[field.physical_name]) {
              errorList.push(`${field.logical_name}を入力してください。`)
            }
            if (field.max_length && freeField[field.physical_name] && String(freeField[field.physical_name]).length > field.max_length) {
              errorList.push(`${field.logical_name}は${field.max_length}桁以内で入力してください。`)
            }
            if (field.max_value && freeField[field.physical_name] && Number(freeField[field.physical_name]) > max_value) {
              errorList.push(`${field.logical_name}は最大${max_value}まで入力してください。`)
            }
            if (field.max_value && freeField[field.physical_name] && Number(freeField[field.physical_name]) > max_value) {
              errorList.push(`${field.logical_name}は最大${max_value}まで入力してください。`)
            }
            if (field.regular_expressions && freeField[field.physical_name]) {
              console.log(field.regular_expressions, freeField[field.physical_name])
              const regex = new RegExp(String(field.regular_expressions))
              if (field.input_type === 'file') {
                freeField[field.physical_name].forEach(file => {
                  console.log('file', file, regex.test(file))
                  if(!regex.test(file)) {
                    errorList.push(`${field.logical_name}の「${file}」は正規表現と一致しません。`)
                  }
                })
              } else {
                if (!(new RegExp(freeField[field.regular_expressions])).test(file)) {
                  errorList.push(`${field.logical_name}は正規表現と一致しません。`)
                }
              }
            }
          });
          if (errorList.length > 0) return Promise.reject({
            status  : 400,
            errors : errorList,
          })
          return Promise.reject({
            status  : 200,
            message : '',
          })
        }
        return Promise.resolve()
      })
    })
    .then(() => {
      console.log('CREATE MEMBER');
      const randomPassword = Common.randomString(10);
      console.log('randomPassword', randomPassword);
      return Base.hashPassword(randomPassword)
        .then(hashPassword => {
          console.log('hashPassword', hashPassword);
          const freeField = Object.assign({}, params.member.freeField);
          return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.CREATE_MEMBER_FUNCTION, [
            Base.extractTenantId(e),
            freeField,
            params.member.bidAllowFlag,
            params.member.emailDeliveryFlag,
            hashPassword,
            1, // require password change
            0, // require confirm token
            null, // token string
            null, // token expire
            Base.extractAdminNo(e),
          ]);
        })
        // .then(() => {
        //   // Send registered information to user's email
        //   const language = params.member.freeField.emailLang || 'en';
        //   return pool.rlsQuery(Base.extractTenantId(e),
        //     'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
        //     [
        //       Base.extractTenantId(e),
        //       [
        //         'EMAIL_TEMPORARY_PASSWORD_INFO_FOR_MEMBER',
        //         'EMAIL_COMMON_FOOTER',
        //         'EMAIL_FROM',
        //       ],
        //       language,
        //     ]
        //   );
        // })
        // .then(constants => {
        //   const mailFrom =
        //     constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 || null;
        //   const footer =
        //     constants.find(x => x.key_string === 'EMAIL_COMMON_FOOTER') || {};
        //   return Promise.all([
        //     Promise.resolve().then(() => {
        //       const mail =
        //         constants.find(
        //           x =>
        //             x.key_string === 'EMAIL_TEMPORARY_PASSWORD_INFO_FOR_MEMBER'
        //         ) || {};
        //       const title = mail.value1;
        //       const sender = mailFrom
        //         ? `"${mailFrom}"<${mail.value2}>`
        //         : mail.value2;
        //       const receivers = [params.member.freeField.email];
        //       const bcc = mail.value3 ? mail.value3.split(',') : [];
        //       const content = Common.format(mail.value4, [
        //         randomPassword,
        //         footer.value4,
        //       ]);

        //       return Common.sendMailBySES(
        //         title,
        //         content,
        //         sender,
        //         receivers,
        //         bcc
        //       );
        //     }),
        //   ]);
        // });
    })
    .then(result => {
      console.log(`result = ${JSON.stringify(result)}`);
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
