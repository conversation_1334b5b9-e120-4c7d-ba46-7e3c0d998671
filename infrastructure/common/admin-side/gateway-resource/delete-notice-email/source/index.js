const async = require('async');
const {DeleteObjectCommand, S3Client} = require('@aws-sdk/client-s3');
const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool();

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);

  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log('delete-notice-email');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => Validator.validation(params))
    .then(() => {
      console.log('Validate sent flag');
      const notice_email_no = params.notice_email_no;
      if (notice_email_no) {
        return Promise.resolve()
          .then(() => {
            console.log('Get original notice email');
            const sql = Define.QUERY.GET_NOTICE_EMAIL_FOR_VALIDATION_FUNCTION;
            return pool.rlsQuery(Base.extractTenantId(e),sql, [Base.extractTenantId(e), notice_email_no]);
          })
          .then(notices => {
            console.log('notices: ', notices);
            const validateResult = {};
            const temp = notices || [];
            temp.map(noticeEmail => {
              if (noticeEmail && noticeEmail.sent_flag === 1) {
                validateResult.sent_flag = 'E000716';
              }
              if (noticeEmail && noticeEmail.delete_flag === 1) {
                validateResult.delete_flag = 'E000717';
              }
            });

            // Validation Fail
            if (Object.keys(validateResult).length > 0) {
              return Promise.reject(
                Validator.createErrorResponse(validateResult)
              );
            }
            // Validation successful
            return Promise.resolve();
          });
      }
      return Promise.resolve();
    })
    .then(() => {
      // Get file list
      const notice_email_no = params.notice_email_no;
      return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_NOTICE_EMAIL_FILES_FUNCTION, [
        Base.extractTenantId(e),
        notice_email_no,
      ]);
    })
    .then(fileList => {
      // Delete physical files
      console.log(`fileList: ${JSON.stringify(fileList)}`);
      if (
        typeof fileList === 'undefined' ||
        fileList === null ||
        fileList.length <= 0
      ) {
        console.log('fileList NULL');
        return Promise.resolve();
      }

      const deleteFiles = [];
      for (let i = 0; i < fileList.length; i++) {
        for (let j = 0; j < fileList[i].file.length; j++) {
          deleteFiles.push(fileList[i].file[j]);
        }
      }

      console.log('delete file from S3');
      const client = new S3Client({});

      // Delete copied files
      return Promise.all(
        deleteFiles.map(file => {
          const command = new DeleteObjectCommand({
            Bucket: process.env.S3_BUCKET,
            Key: file,
          });
          return client.send(command);
        })
      )
        .then(res => {
          console.log('delete file from S3 done', res);
          return Promise.resolve();
        })
        .catch(err => {
          console.log('delete file from S3 error', err);
          return Promise.resolve();
        });
    })
    .then(() => {
      // Update delete flag
      const notice_email_no = params.notice_email_no;
      const admin_no = Base.extractAdminNo(e);
      console.log(`admin_no = ${admin_no}`);

      let sql = '';
      let sql_params = null;

      // Update dalete flag
      sql = Define.QUERY.DELETE_NOTICE_EMAIL_FUNCTION;
      sql_params = [Base.extractTenantId(e), notice_email_no, admin_no];

      console.log(`sql = ${JSON.stringify(sql)}`);
      console.log(`sql_params = ${JSON.stringify(sql_params)}`);

      return pool.rlsQuery(Base.extractTenantId(e),sql, sql_params);
    })
    .then(notice_email_no => {
      const response = {
        data: notice_email_no,
      };
      return Base.createSuccessResponse(cb, response);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
