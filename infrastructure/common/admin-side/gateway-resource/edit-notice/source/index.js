const async = require('async');
const {
  DeleteObjectCommand,
  CopyObjectCommand,
  S3Client,
} = require('@aws-sdk/client-s3');
const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool();

const updateNotice = (index, languageCode, notice, tenant_no, admin_no) => {
  // Update notice
  const sqlParams = [
    notice.notice_no,
    tenant_no,
    notice.display_code,
    languageCode,
    notice.start_datetime,
    notice.end_datetime,
    notice.title[index],
    notice.title1[index],
    notice.sub_title[index],
    notice.body[index],
    notice.link_url[index],
    notice.file[index],
    admin_no,
    admin_no,
  ];
  return pool
    .rlsQuery(tenant_no,Define.QUERY.INSERT_OR_UPDATE_NOTICE_FUNCTION, sqlParams)
    .then(result => {
      console.log('result = ', JSON.stringify(result));
      // [{"f_insert_or_update_notice":215}]
      const noticeNo = result[0].f_insert_or_update_notice;
      return Promise.resolve(noticeNo);
    })
    .catch(error => {
      console.log('error', error);
      return Promise.resolve(null);
    });
};

// Delete all files
const deleteAllFiles = fileList => {
  // Check max length
  if (
    typeof fileList === 'undefined' ||
    fileList === null ||
    fileList.length <= 0
  ) {
    return Promise.resolve();
  }
  const client = new S3Client({region: process.env.AWS_REGION});
  // Delete copied files
  return Promise.all(
    fileList.map(file => {
      const command = new DeleteObjectCommand({
        Bucket: process.env.S3_BUCKET,
        Key: file,
      });
      return client.send(command);
    })
  )
    .then(() => {
      console.log('delete file from S3 done');
      return Promise.resolve();
    })
    .catch(err => {
      console.log('delete file from S3 error', err);
      return Promise.resolve();
    });
};

// Move all files from /notice to /public and vice versa
const moveAllFiles = fileList => {
  // Check files
  if (
    typeof fileList === 'undefined' ||
    fileList === null ||
    fileList.length <= 0
  ) {
    return Promise.resolve();
  }
  const client = new S3Client({
    region: process.env.AWS_REGION,
    signatureVersion: 'v4',
  });

  return Promise.resolve()
    .then(() => {
      // Copy all file
      return Promise.all(
        fileList.map(file => {
          const command = new CopyObjectCommand({
            Bucket: process.env.S3_BUCKET,
            CopySource: `${process.env.S3_BUCKET}/${encodeURIComponent(file.source)}`,
            Key: file.dest,
          });
          return client.send(command);
        })
      )
        .then(res => {
          console.log('copy file done', res);
          return Promise.resolve();
        })
        .catch(err => {
          console.log('copy file from S3 error', err);
          return Promise.resolve();
        });
    })
    .then(() => {
      // Delete copied files
      return Promise.all(
        fileList.map(file => {
          const command = new DeleteObjectCommand({
            Bucket: process.env.S3_BUCKET,
            Key: file.source,
          });
          return client.send(command);
        })
      )
        .then(res => {
          console.log('delete file done', res);
          return Promise.resolve();
        })
        .catch(err => {
          console.log('delete file from S3 error', err);
          return Promise.resolve();
        });
    });
};

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);

  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log('edit-notice');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      // Validation
      const validateResult = [];

      const notice = params.data;
      if (typeof notice === 'undefined' || notice === null) {
        validateResult.push(Define.MESSAGE.E000109);
      }
      // Start date empty
      if (
        typeof notice.start_datetime === 'undefined' ||
        notice.start_datetime === null ||
        notice.start_datetime === ''
      ) {
        validateResult.push(Define.MESSAGE.E000115);
      }
      // End date empty
      if (
        typeof notice.end_datetime === 'undefined' ||
        notice.end_datetime === null ||
        notice.end_datetime === ''
      ) {
        validateResult.push(Define.MESSAGE.E000116);
      }
      // Start date invalid
      let tmpDate = new Date(notice.start_datetime);
      console.log(`tmpDate = ${tmpDate}`);
      if (!(tmpDate instanceof Date && !isNaN(tmpDate))) {
        validateResult.push(Define.MESSAGE.E000117);
      }
      // End date invalid
      tmpDate = new Date(notice.end_datetime);
      console.log(`tmpDate = ${tmpDate}`);
      if (!(tmpDate instanceof Date && !isNaN(tmpDate))) {
        validateResult.push(Define.MESSAGE.E000118);
      }
      // 開始日の大小チェック
      const tmpStDate = new Date(notice.start_datetime);
      const tmpEnDate = new Date(notice.end_datetime);
      if (tmpStDate.getTime() > tmpEnDate.getTime()) {
        console.log('開始日: From > To');
        validateResult.push(Define.MESSAGE.E000123);
      }

      // 日付が1年以内かのチェック
      const today = new Date(Date.now() + (new Date().getTimezoneOffset() + 9 * 60) * 60 * 1000);
      const maxDate = new Date(today);
      maxDate.setDate(today.getDate() + 364);
      maxDate.setHours(0, 0, 0, 0); // 時間をリセットして日付のみ取得
      console.log(`maxDate = ${maxDate}`)
      // 開始日のチェック
      const startDateTime = notice.start_datetime.split(' ');
      const startDateFormatted = startDateTime[0].replace(/\//g, '-');  // 日付のフォーマットをYYYY-MM-DDに変換
      const startDate = new Date(`${startDateFormatted}T00:00:00`);
      const startDateDiff = maxDate - startDate;
      if (startDateDiff < 0) {
        console.log('開始日: 1年以内ではない');
        validateResult.push(Define.MESSAGE.E000184);
      }
      // 終了日のチェック
      const endDateTime = notice.end_datetime.split(' ');
      const endDateFormatted = endDateTime[0].replace(/\//g, '-');  // 日付のフォーマットをYYYY-MM-DDに変換
      const endDate = new Date(`${endDateFormatted}T00:00:00`);
      const endDateDiff = maxDate - endDate;
      if (endDateDiff < 0) {
        console.log('終了日: 1年以内ではない');
        validateResult.push(Define.MESSAGE.E000185);
      }

      // Get language name list
      const languages = notice.language_name;
      if (
        typeof languages === 'undefined' ||
        languages === null ||
        languages.length <= 0
      ) {
        validateResult.push(Define.MESSAGE.E000110);
      }

      // Title empty check
      let isTitleEmpty = true;
      let errorTxt = '';
      for (let i = 0; i < languages.length; i++) {
        if (
          typeof notice.title[i] !== 'undefined' &&
          notice.title[i] !== null &&
          notice.title[i].trim().length > 0
        ) {
          isTitleEmpty = false;
          break;
        }
        if (i > 0) {
          errorTxt += ', ';
        }
        errorTxt += `${languages[i]}のタイトル`;
      }
      if (isTitleEmpty) {
        if (languages.length > 1) {
          validateResult.push(Define.MESSAGE.E000119.replace('#', errorTxt));
        } else {
          validateResult.push(Define.MESSAGE.E000180);
        }
      }

      // Body empty check
      let isBodyEmpty = true;
      errorTxt = '';
      for (let i = 0; i < languages.length; i++) {
        if (
          typeof notice.body[i] !== 'undefined' &&
          notice.body[i] !== null &&
          notice.body[i].trim().length > 0
        ) {
          isBodyEmpty = false;
          break;
        }
        if (i > 0) {
          errorTxt += ', ';
        }
        errorTxt += `${languages[i]}の本文`;
      }
      if (isBodyEmpty) {
        if (languages.length > 1) {
          validateResult.push(Define.MESSAGE.E000119.replace('#', errorTxt));
        } else {
          validateResult.push(Define.MESSAGE.E000181);
        }
      }

      // Validation Fail
      if (validateResult.length > 0) {
        const error = {
          status: 400,
          message: validateResult,
        };
        return Promise.reject(error);
      }

      // Validation successful
      if (params.validation_mode === true) {
        const response = {
          status: 200,
          message: '',
        };
        return Promise.reject(response);
      }
      return Promise.resolve();
    })
    .then(() => {
      const notice = params.data;
      // Get language list
      const languages = notice.language_code;
      if (!languages) {
        const error = {
          status: 400,
          message: Define.MESSAGE.E000110,
        };
        return Promise.reject(error);
      }
      return Promise.all(
        languages.map((lang, index) => {
          return updateNotice(
            index,
            lang,
            notice,
            Base.extractTenantId(e),
            Base.extractAdminNo(e)
          );
        })
      );
    })
    .then(notice => {
      // Then move or delete physical files
      return Promise.resolve()
        .then(() => {
          return moveAllFiles(params.move_files);
        })
        .then(() => {
          return deleteAllFiles(params.delete_files);
        })
        .then(() => Promise.resolve(notice));
    })
    .then(notice => {
      const response = {
        data: notice,
      };
      return Base.createSuccessResponse(cb, response);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
