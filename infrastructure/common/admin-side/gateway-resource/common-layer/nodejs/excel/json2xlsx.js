const nodeExcel = require('./export-excel');

const transform = function (json, config) {
  const conf = transform.prepareJson(json, config);
  const result = nodeExcel.execute(conf);
  return result;
};

// Get a xls type based on js type
transform.getType = function (obj, type) {
  if (type) {
    return type;
  }
  const t = typeof obj;
  switch (t) {
    case 'string':
    case 'number':
      return t;
    case 'boolean':
      return 'bool';
    default:
      return 'string';
  }
};

// Get style index（styleSheet.xmlとxlsx.jsに設定されている）
transform.getStyleIndex = function (type) {
  if (type) {
    return type;
  }
  return 0;
};

// Get a nested property from a JSON object given its key, i.e 'a.b.c'
transform.getByString = function (object, path) {
  path = path.replace(/\[(\w+)\]/g, '.$1'); // Convert indexes to properties
  path = path.replace(/^\./, ''); // Strip a leading dot
  const a = path.split('.');
  while (a.length) {
    const n = a.shift();
    if (n in object) {
      object = typeof object[n] === 'undefined' ? null : object[n];
    } else {
      return null;
    }
  }
  return object;
};

// Prepare json to be in the correct format for excel-export
transform.prepareJson = function (json, config) {
  const res = {};
  const conf = config || {};
  const jsonArr = [].concat(json);
  let fields = conf.fields || Object.keys(jsonArr[0] || {});
  const fieldNames = conf.fieldNames;
  const definedTypes = conf.types || {};
  let types = [];
  if (!(fields instanceof Array)) {
    types = Object.keys(fields).map(key => {
      return fields[key];
    });
    fields = Object.keys(fields);
  }
  // Cols
  res.cols = fields.map((key, i) => {
    const content = jsonArr[0] ? jsonArr[0][key] : '';
    return {
      caption: fieldNames[i] || key,
      type: transform.getType(content, types[i]),
      captionStyleIndex: transform.getStyleIndex(definedTypes[key] || 0),
      beforeCellWrite(row, cellData, eOpt) {
        eOpt.cellType = transform.getType(cellData, types[i]);

        // Xlsx.jsにTYPEを設定すればここでセットする
        const definedType = definedTypes[key] || 0;
        eOpt.styleIndex = transform.getStyleIndex(definedType);

        return cellData;
      },
    };
  });
  // Rows
  res.rows = jsonArr.map(row => {
    return fields.map(key => {
      let value = transform.getByString(row, key);
      // Stringify objects
      if (value && value.constructor === Object) {
        value = JSON.stringify(value);
      }
      // Replace illegal xml characters with a square
      // See http://www.w3.org/TR/xml/#charsets
      // #x9 | #xA | #xD | [#x20-#xD7FF] | [#xE000-#xFFFD] | [#x10000-#x10FFFF]
      if (typeof value === 'string') {
        value = value.replace(
          /[^\u0009\u000A\u000D\u0020-\uD7FF\uE000-\uFFFD\u10000-\u10FFFF]/g,
          ''
        );
      }
      return value;
    });
  });
  // Add style xml if given
  if (conf.style) {
    res.stylesXmlFile = conf.style;
  }
  return res;
};

transform.middleware = function (req, res, next) {
  res.xlsx = function (fn, data, config) {
    const xlsx = transform(data, config);
    res.setHeader('Content-Type', 'application/vnd.openxmlformats');
    res.setHeader('Content-Disposition', `attachment; filename=${fn}`);
    res.end(xlsx, 'binary');
  };
  next();
};

module.exports = transform;
