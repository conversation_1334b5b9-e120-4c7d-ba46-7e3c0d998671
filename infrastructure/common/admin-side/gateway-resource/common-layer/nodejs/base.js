const {
  InvokeCommand,
  LambdaClient,
  LogType,
} = require('@aws-sdk/client-lambda');
const {
  PutObjectCommand,
  GetObjectCommand,
  S3Client,
} = require('@aws-sdk/client-s3');
const {getSignedUrl} = require('@aws-sdk/s3-request-presigner');
const iconv = require('iconv-lite');
const {parseAsync} = require('json2csv');
const json2xlsx = require('./excel/json2xlsx');
const xlsx = require('xlsx');
const Define = require('./define');
const path = require('node:path');
const Bcrypt = require('bcryptjs');
const csv = require('csvtojson');
const {Netmask} = require('netmask');
const Jwt = require('jsonwebtoken');
const request = require('request-promise');
const {Readable} = require('node:stream');
const writeXlsxFile = require('write-excel-file/node');
const Common = require('./common.js');
const {extractFromCognitoGroups} = require('./cognitoUtils');

module.exports = {
  getCommonHeaders() {
    return {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Credentials': true,
      'Access-Control-Allow-Methods': 'GET,POST,OPTIONS',
      'Access-Control-Allow-Headers':
        'Content-Type,Accept,Authorization,Origin',
      'Content-Type': 'application/json',
    };
  },
  /**
   * 管理者IDとパスワードを確認する
   * @param {object} pool DB情報
   * @param {string} adminId 管理者ID
   * @param {string} password パスワード
   * @param origin
   * @returns {Promise} コールバック関数
   */
  // TODO: Cognitoに任せるのでこちらを削除する予定
  passwordVerify(pool, password, adminId, origin) {
    return new Promise((resolve, reject) => {
      let admin = null;
      // アクセス元ドメインの仕様が不安定であるため、現状Origin = nullで検索する
      Promise.resolve()
        .then(() =>
          pool.rlsQuery(Define.QUERY.GET_ADMIN_FOR_LOGIN_FUNCTION, [
            adminId,
            null,
          ])
        )
        .then(result => {
          console.log('result', result);
          if (result.length === 0) {
            const response = {
              status: 401,
              message: Define.MESSAGE.E000004,
            };
            return reject(response);
          }
          admin = result[0];

          return Bcrypt.compare(password, admin.password);
        })
        .then(result => {
          if (result) {
            return resolve(admin);
          }
          const response = {
            status: 401,
            message: Define.MESSAGE.E000004,
          };

          return reject(response);
        })
        .catch(error => {
          return reject(error);
        });
    });
  },

  /**
   * 入力値とBCryptでハッシュ化した値と比較する
   * @param {string} password ハッシュ化するパスワード
   * @returns {Promise} コールバック関数
   */
  hashPassword(password) {
    return new Promise((resolve, reject) => {
      try {
        const saltRounds = 10;
        // ソルト生成
        const salt = Bcrypt.genSaltSync(saltRounds);
        // ハッシュ化
        const hash = Bcrypt.hashSync(password, salt);
        return resolve(hash);
      } catch (error) {
        return reject(error);
      }
    });
  },

  /**
   * IP制限確認
   * @param pool
   * @param {string} accessIp IPアドレス
   * @param tenantNo
   * @returns {Promise} コールバック関数
   */
  checkAccessIpAddress(pool, accessIp, tenantNo) {
    return new Promise((resolve, reject) => {
      console.log(
        `checkAccessIpAddress: accessIp=${accessIp}, tenantNo=${tenantNo}`
      );
      Promise.resolve()
        .then(() =>
          pool.rlsQuery(
            tenantNo,
            Define.QUERY.GET_IP_ADDRESS_FOR_ACCESS_CHECK,
            [tenantNo]
          )
        )
        .then(result => {
          console.log('checkAccessIpAddress: Query result:', result);
          if (!result || result.length === 0) {
            console.log(
              `checkAccessIpAddress: No tenant found for tenant_no=${tenantNo}`
            );
            return reject({
              status: 401,
              message:
                Define.MESSAGE.E800004 ||
                'No IP access configuration found for tenant',
            });
          }

          const allowedIp = result[0].allowed_ip;
          if (!allowedIp) {
            console.log(
              `checkAccessIpAddress: No allowed_ip defined for tenant_no=${tenantNo}`
            );
            return resolve(); // Allow access if no IP restrictions
          }

          const isAllowed = allowedIp
            .split(',')
            .map(x => new Netmask(x.trim()))
            .some(mask => mask.contains(accessIp));
          if (isAllowed) {
            console.log(
              `checkAccessIpAddress: Access granted for IP=${accessIp}`
            );
            return resolve();
          }

          console.log(`checkAccessIpAddress: Access denied for IP=${accessIp}`);
          return reject({
            status: 401,
            message: Define.MESSAGE.E800002 || 'IP address not allowed',
          });
        })
        .catch(error => {
          console.error('checkAccessIpAddress: Database error:', error);
          return reject({
            status: 500,
            message: Define.MESSAGE.E900001 || 'Database error occurred',
            error: error.message,
          });
        });
    });
  },
  /**
   * レスポンス作成
   * @param {object} cb コールバック関数
   * @param {object} response レスポンス
   * @param response
   * @returns {object} コールバック関数を呼ぶ
   */
  createSuccessResponse(cb, response) {
    return Promise.resolve()
      .then(() => this.checkResponseSize(response))
      .then(data => {
        const formatted = {
          statusCode: 200,
          headers: this.getCommonHeaders(),
          body: JSON.stringify(data),
          isBase64Encoded: false,
        };
        return cb(null, formatted);
      })
      .catch(error => this.createErrorResponse(cb, error));
  },
  /**
   * Errorからレスポンス作成
   * @param cb
   * @param {object} error Error
   * @returns {object} エラー内容
   */
  createErrorResponse(cb, error) {
    console.log('「createErrorResponse」エラーが発生しました: ', error);
    let response = error;
    if (!Object.keys(error).includes('status')) {
      response = {
        statusCode: 400,
        headers: this.getCommonHeaders(),
        message: Define.MESSAGE.E900001,
      };
      return cb(null, response);
    }

    // If the error status is 200, return it directly as a success response
    if (response.status === 200) {
      const formatted = {
        statusCode: 200,
        headers: this.getCommonHeaders(),
        body: JSON.stringify(response),
        isBase64Encoded: false,
      };
      return cb(null, formatted);
    }

    // Handle other status codes (e.g., 400,406,500, etc.)
    return Promise.resolve()
      .then(() => {
        // If it's an internal server error (500), send a notification to Slack
        if (response.status >= 500) {
          return this.noticeToSlack(error);
        }
        return Promise.resolve();
      })
      .then(() => {
        const formatted = {
          statusCode: response.status, // Generally, the status code is 400,406,500
          headers: this.getCommonHeaders(),
          body: JSON.stringify(response),
          isBase64Encoded: false,
        };
        return cb(null, formatted);
      })
      .catch(() => {
        const formatted = {
          statusCode: response.status,
          headers: this.getCommonHeaders(),
          body: JSON.stringify(response),
          isBase64Encoded: false,
        };
        return cb(null, formatted);
      });
  },

  checkResponseSize(response) {
    return new Promise((resolve, reject) => {
      console.log('checkResponseSize: ', JSON.stringify(response || '').length);
      if (
        response &&
        JSON.stringify(response).length >
          Number.parseInt(process.env.MAX_RESPONSE_SIZE, 10)
      ) {
        return reject({
          status: 413,
          message: Define.MESSAGE.E700001,
        });
      }
      return resolve(response);
    });
  },
  /**
   * トークン発行
   * @param {Json} data トークンに含まれるデータ
   * @returns {object} - JWTトークン
   */
  jwtSign(data) {
    return Jwt.sign(data, process.env.JWT_KEY, {
      expiresIn: process.env.TOKEN_EXPIRES,
    });
  },
  /**
   * InvokeType確認
   * @param {Json} e lambdaエベントオブジェクト
   * @returns {Promise} コールバック関数
   */
  startRequest(e) {
    return new Promise((resolve, reject) => {
      console.log(
        `[${process.env.LAMBDA_FUNCTION_NAME}] From IP address: ${e.requestContext.identity.sourceIp}`
      );
      return resolve();
    });
  },
  noticeToSlack(error) {
    return new Promise((resolve, reject) => {
      const region = process.env.AWS_REGION || 'ap-northeast-1';
      const functionName = process.env.AWS_LAMBDA_FUNCTION_NAME || '';
      const logGroupName = `/aws/lambda/${functionName}`;
      const encodedLogGroupName = encodeURIComponent(logGroupName);
      const cloudwatchLink = `https://${region}.console.aws.amazon.com/cloudwatch/home?region=${region}#logsV2:log-groups/log-group/${encodedLogGroupName}`;
      const message = `システムエラーが発生しました。
        - リクエスト名：${functionName}
        - エラー内容：${error ? error.message || JSON.stringify(error) : 'エラーメッセージが取得できません。'}
        - ログリンク：${cloudwatchLink}`;
      return this.invokeLambda(process.env.NOTICE_SLACK_FUNCTION, {
        message,
      })
        .then(resolve)
        .catch(error => {
          console.log(error);
          return resolve();
        });
    });
  },
  invokeLambda(functionArn, event) {
    const client = new LambdaClient({});
    const command = new InvokeCommand({
      FunctionName: functionArn,
      Payload: JSON.stringify(event),
      InvocationType: 'RequestResponse',
      LogType: LogType.Tail,
    });
    return Promise.resolve()
      .then(() => client.send(command))
      .then(({Payload, LogResult}) => {
        const payload = Buffer.from(Payload).toJSON();
        const logs = Buffer.from(LogResult, 'base64').toString();
        console.log('Payload: ', payload);
        console.log('Logs: ', logs);
        if (payload.errorType || payload.errorMessage) {
          return reject(payload.errorMessage);
        }
        return Promise.resolve(payload);
      });
  },
  /**
   * Randomの文字列を作成する
   * @param length 文字列の長さ
   */
  randomString(length) {
    let result = '';
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  },
  /**
   * Csv,Xlsx項目名を変換する
   * 例：CSVの項目名が「商品名」、COLUMN_NAMEが「{item_name: 商品名}」の場合、「商品名」を「item_name」に変換する
   * @param {object} data CSVコンテンツ（Jsonの形）
   * @param {object} COLUMN_NAME CSV項目名定数マスター
   * @returns {Promise} コールバック関数
   */
  mapColumnNameToId(data, COLUMN_NAME) {
    if (data && data.length > 0) {
      // Create new order following COLUMN_NAME's order
      const keys = Object.keys(COLUMN_NAME);
      const values = Object.values(COLUMN_NAME);
      const newData = [];
      for (const json of data) {
        const tmpLine = {};
        // Values content csv's header title usually in japanese, so we need to convert it to english column name
        values.forEach((key, i) => {
          tmpLine[keys[i]] = json[key];
        });
        newData.push(tmpLine);
      }
      return Promise.resolve(newData);
    }
    return Promise.reject({
      status: 400,
      message: Define.MESSAGE.E000166,
    });
  },

  /**
   * Csv,Xlsx項目名を変換する
   * 例：CSVの項目名が「item_name」、COLUMN_NAMEが「{item_name: 商品名}」の場合、「item_name」を「商品名」に変換する
   * @param {object} data CSVコンテンツ（Jsonの形）
   * @param {object} COLUMN_NAME CSV項目名定数マスター
   * @returns {Promise} コールバック関数
   */
  mapIdToColumnName(data, COLUMN_NAME) {
    if (data && data.length > 0) {
      const keys = Object.keys(COLUMN_NAME);
      const values = Object.values(COLUMN_NAME);
      const newData = data.map(json => {
        const tmpLine = {};
        keys.forEach((key, i) => {
          tmpLine[values[i]] = json[key];
        });
        return tmpLine;
      });
      return Promise.resolve(newData);
    }
    return Promise.reject({
      status: 400,
      message: Define.MESSAGE.E000166,
    });
  },

  /**
   * CSVコンテンツ作成
   * @param {object} data CSVコンテンツ（Jsonの形）
   * @returns {Promise} コールバック関数
   */
  createCsvContentNoConvertHeader(data) {
    return new Promise((resolve, reject) => {
      Promise.resolve(data.map(x => x.csv_row).join('\r\n'))
        .then(() => {
          const field = Object.values(data[0]);
          const opts = {field};
          return parseAsync(data, opts);
        })
        .then(csv => {
          csv = `${csv.split('\n').join('\r\n')}\r\n`;
          return resolve(iconv.encode(csv, 'Shift_JIS'));
        })
        .catch(error => {
          console.log(error);
          return reject(error);
        });
    });
  },
  /**
   * CSVコンテンツ作成
   * @param {object} data CSVコンテンツ（Jsonの形）
   * @param {object} CSV_DEFINE CSVの定数
   * @returns {Promise} コールバック関数
   */
  createCsvContent(data, CSV_DEFINE) {
    return new Promise((resolve, reject) => {
      Promise.resolve(data.map(x => x.csv_row).join('\r\n'))
        .then(() => {
          return this.mapIdToColumnName(data, CSV_DEFINE.COLUMN_NAME);
        })
        .then(result => {
          const field = Object.values(result[0]);
          const opts = {field};
          return parseAsync(result, opts);
        })
        .then(csv => {
          csv = `${csv.split('\n').join('\r\n')}\r\n`;
          return resolve(iconv.encode(csv, 'Shift_JIS'));
        })
        .catch(error => {
          console.log(error);
          return reject(error);
        });
    });
  },
  /**
   * Jsonの形からCSVに変換し、S3にアップロードする
   * @param {object} data CSVコンテンツ（Jsonの形）
   * @param s3Key
   * @returns {Promise} コールバック関数
   */
  uploadCsvToS3NoConvertHeader(data, s3Key) {
    const client = new S3Client({});
    return Promise.resolve(data.map(x => x.csv_row).join('\r\n'))
      .then(() => {
        const field = Object.values(data[0]);
        const opts = {field};
        return parseAsync(data, opts);
      })
      .then(csv => {
        csv = `${csv.split('\n').join('\r\n')}\r\n`;
        const command = new PutObjectCommand({
          Bucket: process.env.S3_BUCKET,
          Key: s3Key,
          Body: iconv.encode(csv, 'Shift_JIS'),
          ContentType: 'text/csv charset=Shift_JIS',
          ContentDisposition: 'attachment',
        });
        // Upload file to s3
        return client.send(command);
      })
      .then(() => {
        // get pre-signed url
        const expiresIn = Number.parseInt(process.env.CSV_EXPIRES, 10);
        const command = new GetObjectCommand({
          Bucket: process.env.S3_BUCKET,
          Key: s3Key,
        });
        return getSignedUrl(client, command, {expiresIn});
      });
  },
  /**
   * Jsonの形からCSVに変換し、S3にアップロードする
   * @param {object} data CSVコンテンツ（Jsonの形）
   * @param {object} CSV_DEFINE CSVの定数
   * @param csvFile
   * @returns {Promise} コールバック関数
   */
  uploadCsvToS3(data, CSV_DEFINE, csvFile) {
    const client = new S3Client({});
    const s3Key =
      csvFile ||
      Common.format(CSV_DEFINE.KEY_FORMAT, [
        Common.dateToStringWithDelimiter(new Date()),
        this.randomString(10),
      ]);
    return Promise.resolve(data.map(x => x.csv_row).join('\r\n'))
      .then(() => {
        return this.mapIdToColumnName(data, CSV_DEFINE.COLUMN_NAME);
      })
      .then(result => {
        const field = Object.values(result[0]);
        const opts = {field};
        return parseAsync(result, opts);
      })
      .then(csv => {
        csv = `${csv.split('\n').join('\r\n')}\r\n`;
        const command = new PutObjectCommand({
          Bucket: process.env.S3_BUCKET,
          Key: s3Key,
          Body: iconv.encode(csv, 'Shift_JIS'),
          ContentType: 'text/csv charset=Shift_JIS',
          ContentDisposition: 'attachment',
        });
        // Upload file to s3
        return client.send(command);
      })
      .then(() => {
        // get pre-signed url
        const expiresIn = Number.parseInt(process.env.CSV_EXPIRES, 10);
        const getCommand = new GetObjectCommand({
          Bucket: process.env.S3_BUCKET,
          Key: s3Key,
        });
        return getSignedUrl(client, getCommand, {expiresIn});
      });
  },
  parseS3Csv(bucket, objectKey) {
    const client = new S3Client({});
    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: objectKey,
    });
    return new Promise((resolve, reject) => {
      client.send(command).then(({Body}) => {
        const jsonArr = [];
        const readableStream = Readable.fromWeb(Body?.transformToWebStream());
        return readableStream
          .pipe(iconv.decodeStream('Shift_JIS'))
          .pipe(
            csv().on('data', data => {
              const row = JSON.parse(iconv.decode(Buffer.from(data), 'utf-8'));
              jsonArr.push(row);
            })
          )
          .on('end', () => {
            return resolve(jsonArr);
          })
          .on('error', err => {
            console.log('error', err);
            return reject(err);
          });
      });
    });
  },
  getFilenameFromUrl(url) {
    const anchor = url.indexOf('#');
    const query = url.indexOf('?');
    const end = Math.min(
      anchor > 0 ? anchor : url.length,
      query > 0 ? query : url.length
    );
    return url.substring(url.lastIndexOf('/', end) + 1, end);
  },
  createXlsxContent() {
    return Promise.resolve(data.map(x => x.csv_row).join('\r\n')).then(() => {
      const fields = Object.keys(CSV_DEFINE.COLUMN_NAME);
      const fieldNames = Object.values(CSV_DEFINE.COLUMN_NAME);
      const xlsx = json2xlsx(data, {
        fields,
        fieldNames,
      });
      return Promise.resolve(xlsx);
    });
  },
  // Local lib ver
  generateXlsDataLocal(data, XLSX_DEFINE, xlsxFile) {
    return new Promise((resolve, reject) => {
      const fields = Object.keys(XLSX_DEFINE.COLUMN_NAME);
      const fieldNames = Object.values(XLSX_DEFINE.COLUMN_NAME);
      const fieldTypes = XLSX_DEFINE.TYPE;
      const styleSheet = path.join(__dirname, '/excel/styleSheet.xml');

      const xlsx = json2xlsx(data, {
        fields,
        fieldNames,
        types: fieldTypes,
        style: styleSheet,
      });
      const buff = Buffer.from(xlsx, 'binary');
      return resolve(buff);
    });
  },
  // Write-excel-file ver
  generateXlsData2(data, XLSX_DEFINE, xlsxFile) {
    const genXls = async (resolve, reject) => {
      const columns = Object.keys(XLSX_DEFINE.COLUMN_NAME);
      const fieldNames = Object.values(XLSX_DEFINE.COLUMN_NAME);

      // Prepare xls data
      const xlsData = [];
      // Header
      xlsData.push(
        fieldNames.map(field => {
          const ret = {};
          ret.value = field;
          ret.font = 'Yu Gothic';
          ret.type = String;
          ret.alignVertical = 'center';
          return ret;
        })
      );
      // Rows data
      data.map(row => {
        xlsData.push(
          columns.map(col => {
            const colData = {};
            colData.value = row[col];
            colData.font = 'Yu Gothic';
            if (typeof colData.value === 'number') {
              colData.type = Number;
              colData.format = '0';
            } else {
              colData.type = String;
            }
            colData.alignVertical = 'center';
            return colData;
          })
        );
      });

      // Build xls file
      const stream = await writeXlsxFile(xlsData, {
        fontFamily: 'Yu Gothic',
      });
      const strData = [];
      stream.on('data', chunk => {
        strData.push(chunk);
      });
      stream.on('error', err => {
        console.log(err);
        return reject(err);
      });
      stream.on('end', () => {
        const buffer = Buffer.concat(strData);
        return resolve(buffer);
      });
    };

    return new Promise((resolve, reject) => {
      return genXls(resolve, reject);
    });
  },
  /**
   * Jsonの形からXlsxに変換し、S3にアップロードする
   * @param {object} data Xlsxコンテンツ（Jsonの形）
   * @param {object} CSV_DEFINE CSVの定数
   * @param XLSX_DEFINE
   * @param xlsxFile
   * @returns {Promise} コールバック関数
   */
  uploadXlsxToS3(data, XLSX_DEFINE, xlsxFile) {
    const client = new S3Client({});
    const s3Key =
      xlsxFile ||
      Common.format(XLSX_DEFINE.KEY_FORMAT, [
        Common.dateToString(new Date()),
        this.randomString(10),
      ]);
    return Promise.resolve(data.map(x => x.csv_row).join('\r\n'))
      .then(() => {
        return this.generateXlsDataLocal(data, XLSX_DEFINE, xlsxFile);
        // Return this.generateXlsData2(data, XLSX_DEFINE, xlsxFile)
      })
      .then(buffer => {
        const command = new PutObjectCommand({
          Bucket: process.env.S3_BUCKET,
          Key: s3Key,
          Body: buffer,
        });
        // Upload file to s3
        return client.send(command);
      })
      .then(() => {
        // get pre-signed url
        const expiresIn = Number.parseInt(process.env.CSV_EXPIRES, 10);
        const getCommand = new GetObjectCommand({
          Bucket: process.env.S3_BUCKET,
          Key: s3Key,
        });
        return getSignedUrl(client, getCommand, {expiresIn});
      });
  },
  parseS3Xlsx(bucket, objectKey) {
    const client = new S3Client({});
    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: objectKey,
    });
    return client
      .send(command)
      .then(({Body}) => {
        return Body.transformToByteArray();
      })
      .then(data => {
        const workbook = xlsx.read(Buffer.from(data, 'binary'));
        const SheetNames = workbook.SheetNames;
        if (SheetNames && SheetNames.length > 0) {
          const jsonArr = xlsx.utils.sheet_to_json(
            workbook.Sheets[SheetNames[0]],
            {
              defval: '',
              raw: true,
            }
          );
          return Promise.resolve(jsonArr);
        }
        const err = {
          status: 400,
          message: Define.MESSAGE.E900001,
        };
        return Promise.reject(err);
      });
  },
  // 値が「-」(数値以外)や空文字の場合は単位を表示しないようにする
  number2string(val, nullText = '', unit = '', NaNVal) {
    if (
      typeof val === 'undefined' ||
      val === null ||
      String(val).length === 0
    ) {
      return nullText;
    }
    if (isNaN(val)) {
      return typeof NaNVal === 'undefined' ? String(val) : NaNVal;
    }
    return Common.numberStringWithComma(val) + unit;
  },
  // Null to string
  nullToString(val, defaultVal = '') {
    const ret = typeof val !== 'undefined' && val !== null ? val : defaultVal;
    return ret;
  },

  /**
   * Parses the request body if it’s JSON, otherwise returns it as-is.
   * @param {string|object} body - The raw request body from the event.
   * @returns {object|string} - Parsed JSON object or original body.
   */
  parseRequestBody(body) {
    if (typeof body === 'string') {
      try {
        return JSON.parse(body);
      } catch (error) {
        return body;
      }
    }
    // If body is already an object (e.g., from non-JSON content), use as-is
    return body;
  },

  /**
   * Request から tenant ID を抽出する
   * Authenticated users: Cognito claims から抽出する. exg. custom:tenant_id:1
   * Unauthenticated users: ホスト名またはクエリパラメータから抽出する
   * @param {Object} event - Lambda イベントオブジェクト
   * @returns {number|string} - テナント ID
   */
  extractTenantId(event) {
    const claims = event?.requestContext?.authorizer?.claims;
    if (claims) {
      const result = extractFromCognitoGroups(event, 'tenantId');
      if (result.error || !result.data) return this.noticeToSlack(result);
      return result.data;
    }
    const tenantResolver = require('./tenant-resolver.js');
    const result = tenantResolver.resolveTenant(event);
    if (result.error || !result.data) return this.noticeToSlack(result);
    return result.data;
  },

  // Use the helper function for all extract methods
  extractAdminNo(event) {
    const result = extractFromCognitoGroups(event, 'adminNo');
    if (result.error || !result.data) return this.noticeToSlack(result);
    return result.data;
  },

  extractMemberNo(event) {
    const result = extractFromCognitoGroups(event, 'memberNo');
    if (result.error || !result.data) return this.noticeToSlack(result);
    return result.data;
  },

  extractAdminLanguageCode(event) {
    const result = extractFromCognitoGroups(event, 'adminLanguageCode');
    if (result.error || !result.data) return this.noticeToSlack(result);
    return result.data;
  },

  extractRoleId(event) {
    const result = extractFromCognitoGroups(event, 'roleId');
    if (result.error || !result.data) return this.noticeToSlack(result);
    return result.data;
  },

  extractAdminName(event) {
    const result = extractFromCognitoGroups(event, 'adminName');
    if (result.error || !result.data) return this.noticeToSlack(result);
    return result.data;
  },
};
