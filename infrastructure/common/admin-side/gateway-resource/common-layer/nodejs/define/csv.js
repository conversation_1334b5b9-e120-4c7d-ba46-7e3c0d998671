const CSV_LIST = {
  EXPORT: {
    // 会員一覧
    MEMBER_CSV: {
      COLUMN_NAME: {
        memberId: '会員ID',
        memberRequestNo: '会員申請番号',
        customerCode: '取引先コード',
        country: '国',
        companyName: '会社名・団体名',
        companyNameKana: '会社名カタカナ',
        ceoName: '代表者名前',
        ceoNameKana: '代表者名前カナ',
        memberName: '担当者名前',
        memberLastName: '担当者苗字',
        companyAddress: '会社住所',
        establishmentDate: '会社設立日',
        email: 'メールアドレス',
        tel: '電話番号',
        companyHp: '会社HP',
        invoiceNo: 'インボイス番号',
        antiquePermitNo: '古物商許可証許可番号',
        whatsApp: 'WhatsApp',
        wechat: 'Wechat',

        lastUpdateAdmin: 'ステータス設定者',
        statusName: 'ステータス',
        bidAllowFlag: 'WEB応札',
        emailDeliveryFlag: 'メール配信',
        memo: '備考',
        createDatetime: '登録日時',
        updateDatetime: '更新日時',
        lastLoginDatetime: '最終ログイン日時',
      },
      KEY_FORMAT: 'csv-download/{0}-{1}/会員情報.xlsx',
    },
    BID_HISTORY_CSV: {
      COLUMN_NAME: {
        bid_datetime: '入札日時',
        product_id: '商品ID',
        product_name: '商品名',
        member_id: '会員ID',
        customer_code: '取引先ID',
        company_name: '会社名',
        bid_price: '入札単価',
        bid_quantity: '入札数量',
      },
      KEY_FORMAT: 'csv-download/{0}-{1}/入札履歴.csv',
    },
    BID_ORDER_CSV: {
      COLUMN_NAME: {
        product_id: '商品ID',
        product_name: '商品名',
        quantity: '数量',
        lowest_bid_price: '最低入札単価',
        lowest_bid_quantity: '最低入札数量',
        lowest_bid_accept_price: '最低落札単価',
        lowest_bid_accept_quantity: '最低落札数量',
        // current_price              : '現在価格',
        member_id_1: '会員ID１',
        customer_code_1: '取引先ID１',
        company_name_1: '会社名１',
        bid_price_1: '入札単価１',
        bid_quantity_1: '入札数量１',
        member_id_2: '会員ID２',
        customer_code_2: '取引先ID２',
        company_name_2: '会社名２',
        bid_price_2: '入札単価２',
        bid_quantity_2: '入札数量２',
        member_id_3: '会員ID３',
        customer_code_3: '取引先ID３',
        company_name_3: '会社名３',
        bid_price_3: '入札単価３',
        bid_quantity_3: '入札数量３',
        member_id_4: '会員ID４',
        customer_code_4: '取引先ID４',
        company_name_4: '会社名４',
        bid_price_4: '入札単価４',
        bid_quantity_4: '入札数量４',
        member_id_5: '会員ID５',
        customer_code_5: '取引先ID５',
        company_name_5: '会社名５',
        bid_price_5: '入札単価５',
        bid_quantity_5: '入札数量５',
        member_id_6: '会員ID６',
        customer_code_6: '取引先ID６',
        company_name_6: '会社名６',
        bid_price_6: '入札単価６',
        bid_quantity_6: '入札数量６',
        member_id_7: '会員ID７',
        customer_code_7: '取引先ID７',
        company_name_7: '会社名７',
        bid_price_7: '入札単価７',
        bid_quantity_7: '入札数量７',
        member_id_8: '会員ID８',
        customer_code_8: '取引先ID８',
        company_name_8: '会社名８',
        bid_price_8: '入札単価８',
        bid_quantity_8: '入札数量８',
        member_id_9: '会員ID９',
        customer_code_9: '取引先ID９',
        company_name_9: '会社名９',
        bid_price_9: '入札単価９',
        bid_quantity_9: '入札数量９',
        member_id_10: '会員ID１０',
        customer_code_10: '取引先ID１０',
        company_name_10: '会社名１０',
        bid_price_10: '入札単価１０',
        bid_quantity_10: '入札数量１０',
      },
      KEY_FORMAT: 'csv-download/{0}-{1}/入札順位.csv',
    },
    BID_RESULT_CSV: {
      COLUMN_NAME: {
        // manageNo                : '台帳No',
        // lotId                   : 'ロットNo',
        // category                : 'カテゴリー',
        productId: '商品ID',
        productName: '商品名',
        quantity: '数量',
        lowestBidPrice: '最低入札単価',
        lowestBidQuantity: '最低入札数量',
        lowestBidAcceptPrice: '最低落札単価',
        lowestBidAcceptQuantity: '最低落札数量',
        bidSuccessMemberId: '会員ID',
        customerCode: '取引先ID',
        companyName: '会社名',
        bidPrice: '入札単価',
        bidQuantity: '入札数量',
        totalBidPrice: '入札合計',
        // bidSuccessPrice         : '落札金額',
        bidSuccessQuantity: '落札数量',
        // bidSuccessPriceTaxIn    : '落札合計',
        // taxRate                 : '税率',
        hummerFlag: '結果',
      },
      KEY_FORMAT: 'csv-download/{1}-{2}/入札結果.csv',
    },
    EXHIBITION_STATUS_LIST_CSV: {
      COLUMN_NAME: {
        ubrand_code: '管理番号',
        product_name: '商品名',
        current_price: '現在価格',
        bid_count: '入札数',
        end_datetime: '終了予定日',
      },
      KEY_FORMAT: 'csv-download/{1}-{2}/商品一覧.csv',
    },
    // 出品
    EXHIBITION: {
      COLUMN_NAME: {
        manage_no: '商品ID',
        category: 'カテゴリー',
        maker: 'メーカー',
        model: '型番',
        product_name: '商品名',
        capacity: '容量',
        rank: 'グレード',
        sim: 'SIM',
        quantity: '数量',
        note1: '備考欄1',
        note2: '備考欄2',
        note1_en: '備考欄1(英語)',
        note2_en: '備考欄2(英語)',
        lowest_bid_quantity: '最低入札数量',
        lowest_bid_price: '最低入札単価',
        lowest_bid_accept_quantity: '最低落札数量',
        lowest_bid_accept_price: '最低落札単価',
        sku_id: 'SKUID',
      },
      KEY_FORMAT: 'csv-download/{1}-{2}/出展機.csv',
    },
  },
  IMPORT: {
    // 出品
    EXHIBITION: {
      COLUMN_NAME: {
        manage_no: '商品ID',
        category: 'カテゴリー',
        maker: 'メーカー',
        product_name: '商品名',
        capacity: '容量',
        rank: 'グレード',
        sim: 'SIM',
        quantity: '数量',
        note1: '備考欄1',
        note2: '備考欄2',
        note1_en: '備考欄1(英語)',
        note2_en: '備考欄2(英語)',
        lowest_bid_quantity: '最低入札数量',
        lowest_bid_price: '最低入札単価',
        lowest_bid_accept_quantity: '最低落札数量',
        lowest_bid_accept_price: '最低落札単価',
        sku_id: 'SKUID',
      },
    },
    MEMBER: {
      COLUMN_NAME: {
        memberId: '会員ID',
        customerCode: '取引先コード',
        country: '国',
        companyName: '会社名・団体名',
        companyNameKana: '会社名カタカナ',
        ceoName: '代表者名前',
        ceoNameKana: '代表者名前カナ',
        memberName: '担当者名前',
        memberLastName: '担当者苗字',
        companyAddress: '会社住所',
        establishmentDate: '会社設立日',
        email: 'メールアドレス',
        tel: '電話番号',
        companyHp: '会社HP',
        invoiceNo: 'インボイス番号',
        antiquePermitNo: '古物商許可証許可番号',
        whatsApp: 'WhatsApp',
        wechat: 'Wechat',

        lastUpdateAdmin: 'ステータス設定者',
        statusName: 'ステータス',
        bidAllowFlag: 'WEB応札',
        emailDeliveryFlag: 'メール配信',
        memo: '備考',
      },
    },
  },
};

module.exports = CSV_LIST;
