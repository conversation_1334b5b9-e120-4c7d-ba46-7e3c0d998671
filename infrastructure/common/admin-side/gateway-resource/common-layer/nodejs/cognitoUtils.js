/**
 * Extracts a value from Cognito claims in the event object based on the provided field.
 * For cognito:groups, assumes a string format "<key>:<value>,...". For custom attributes, uses raw values.
 * @param {Object} event - Lambda event object
 * @param {string} field - Field to extract (e.g., 'tenantId', 'adminNo')
 * @returns {number | string} - Extracted value
 * @throws {Error} - If claims are missing, invalid, or value not found
 */
function createResponse(error, errorMessage, data) {
  if (!error) {
    return {
      error: false,
      data,
    };
  }
  console.log(errorMessage);
  return {
    error: true,
    message: errorMessage,
  };
}

function extractFromCognitoGroups(event, field) {
  const claims = event?.requestContext?.authorizer?.claims;
  console.log('cognito claims: ', JSON.stringify(claims, null, 2));
  switch (field) {
    // tenant-id : number
    case 'tenantId': {
      const cognitoGroups = claims['cognito:groups'];
      const match = cognitoGroups.toString().match(/tenant-id:(\d+)/);
      if (!match || !match[1]) {
        return createResponse(
          true,
          'Tenant ID not found in Cognito claims',
          null
        );
      }
      return createResponse(false, null, parseInt(match[1], 10));
    }

    // admin_no : number
    case 'adminNo': {
      const adminNo = claims['custom:admin_no'];
      if (adminNo) return createResponse(false, null, parseInt(adminNo, 10));
      return createResponse(true, 'Admin No not found in Cognito claims', null);
    }

    // member_no : number
    case 'memberNo': {
      const memberNo = claims['custom:member_no'];
      if (memberNo) return createResponse(false, null, parseInt(memberNo, 10));
      return createResponse(
        true,
        'Member No not found in Cognito claims',
        null
      );
    }

    // admin_language_code : string
    case 'adminLanguageCode': {
      const code = claims['custom:admin_language_code'];
      if (code) return createResponse(false, null, code);
      return createResponse(
        true,
        'Admin Language Code not found in Cognito claims',
        null
      );
    }

    // role_id : string , (10:管理者,20:運用担当者,30:一般)
    case 'roleId': {
      const roleId = claims['custom:role_id'];
      if (roleId) return createResponse(false, null, roleId);
      return createResponse(true, 'Role ID not found in Cognito claims', null);
    }

    // admin_name : string
    case 'adminName': {
      const adminName = claims['custom:admin_name'];
      if (adminName) return createResponse(false, null, adminName);
      return createResponse(
        true,
        'Admin Name not found in Cognito claims',
        null
      );
    }

    default:
      return createResponse(true, `Invalid field: ${field}`, null);
  }
}

module.exports = {
  extractFromCognitoGroups,
};
