const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);
  console.log('export-member-csv-file');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      // Get constant
      console.log('GET CONSTANT');
      const sqlParams = [
        ['COUNTRY_CODE', 'MEMBER_STATUS_CODE'],
        Base.extractTenantId(e),
        Base.extractAdminLanguageCode(e),
      ];
      return pool
        .rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_CONSTANTS_BY_KEYS, sqlParams)
        .then(constants => {
          const COUNTRY_CODE = {};
          const MEMBER_STATUS_CODE = {};
          const BID_ALLOW_FLAG = {};
          const EMAIL_DELIVERY_FLAG = {};

          COUNTRY_CODE[''] = '';
          MEMBER_STATUS_CODE[''] = '';
          BID_ALLOW_FLAG[''] = '';
          EMAIL_DELIVERY_FLAG[''] = '';

          // Bid allow flag
          BID_ALLOW_FLAG['0'] = '否';
          BID_ALLOW_FLAG['1'] = '可';

          // Email delivery flag
          EMAIL_DELIVERY_FLAG['0'] = '送信しない';
          EMAIL_DELIVERY_FLAG['1'] = '送信する';

          for (let i = 0; i < constants.length; i++) {
            switch (constants[i].key_string) {
              case 'COUNTRY_CODE':
                COUNTRY_CODE[constants[i].value1] = constants[i].value2;
                break;
              case 'MEMBER_STATUS_CODE':
                MEMBER_STATUS_CODE[constants[i].value1] = constants[i].value2;
                break;
              default:
                console.log('dafault');
            }
          }

          return Promise.resolve({
            MEMBER_STATUS_CODE,
            COUNTRY_CODE,
            BID_ALLOW_FLAG,
            EMAIL_DELIVERY_FLAG,
          });
        });
    })
    .then(constants => {
      // Get member list
      console.log('GET MEMBERS');
      console.log(`constants = ${JSON.stringify(constants)}`);
      // Get member list
      return pool
        .rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_MEMBERS_CSV_FUNCTION, [
          Base.extractTenantId(e),
          params.customerCode || null,
          params.companyName || null,
          params.memberStatus || null,
        ])
        .then(members => {
          const convertedMembers = members.map(mem => {
            const country =
              mem.free_field.country && constants.COUNTRY_CODE
                ? constants.COUNTRY_CODE[mem.free_field.country]
                : '';
            const statusName =
              !Common.isEmpty(mem.status) && constants.MEMBER_STATUS_CODE
                ? constants.MEMBER_STATUS_CODE[mem.status]
                : '';
            const bidAllowFlag =
              !Common.isEmpty(mem.bid_allow_flag) && constants.BID_ALLOW_FLAG
                ? constants.BID_ALLOW_FLAG[mem.bid_allow_flag]
                : '';
            const emailDeliveryFlag =
              !Common.isEmpty(mem.email_delivery_flag) &&
              constants.EMAIL_DELIVERY_FLAG
                ? constants.EMAIL_DELIVERY_FLAG[mem.email_delivery_flag]
                : '';

            return {
              memberId: mem.member_id,
              memberRequestNo: mem.member_request_no,
              customerCode: mem.free_field.customerCode || '',
              country,
              companyName: mem.free_field.companyName || '',
              companyNameKana: mem.free_field.companyNameKana || '',
              ceoName: mem.free_field.ceoName || '',
              ceoNameKana: mem.free_field.ceoNameKana || '',
              ceoBirthday: mem.free_field.ceoBirthday || '',
              memberName: mem.free_field.memberName || '',
              memberLastName: mem.free_field.memberLastName || '',
              companyAddress: mem.free_field.companyAddress || '',
              establishmentDate: mem.free_field.establishmentDate || '',
              email: mem.free_field.email || '',
              emailLang: mem.free_field.emailLang || '',
              telCountryCode: mem.free_field.telCountryCode || '',
              tel: mem.free_field.tel || '',
              companyHp: mem.free_field.companyHp || '',
              invoiceNo: mem.free_field.invoiceNo || '',
              antiquePermitNo: mem.free_field.antiquePermitNo || '',
              antiquePermitDate: mem.free_field.antiquePermitDate || '',
              antiquePermitCommission:
                mem.free_field.antiquePermitCommission || '',
              whatsApp: mem.free_field.whatsApp || '',
              wechat: mem.free_field.wechat || '',
              businessContent: mem.free_field.businessContent || '',
              memo: mem.free_field.memo || '',
              lastUpdateAdmin: mem.last_user_name || '',
              statusName,
              bidAllowFlag,
              emailDeliveryFlag,
              createDatetime: mem.create_datetime,
              updateDatetime: mem.update_datetime,
              lastLoginDatetime: mem.last_login_datetime,
            };
          });

          return Promise.resolve(convertedMembers);
        });
    })
    .then(members => {
      console.log('SORTING');
      // Sort
      if (
        typeof members !== 'undefined' &&
        members !== null &&
        members.length > 0
      ) {
        // Check sorter
        const sorter = params.sorter;
        if (typeof sorter === 'undefined' || sorter === null) {
          return Promise.resolve(members);
        }

        // Prepare data for sorting
        members.map(mem => {
          // Convert null to string
          for (const key of Object.keys(mem)) {
            if (typeof mem[key] === 'undefined' || mem[key] === null) {
              mem[key] = '';
              console.log(`${key} = ${mem[key]}`);
            }
          }
          return mem;
        });

        console.log(`BEFORE SORTING: ${JSON.stringify(members)}`);

        // Sort array
        const col = sorter.column;
        // If values in column are to be sorted by numeric value they all have to be type number
        const flip = sorter.asc ? 1 : -1;

        const sorted_members = members.sort((item, item2) => {
          let value =
            typeof item[col] === 'undefined' || item[col] === null
              ? ''
              : item[col];
          let value2 =
            typeof item2[col] === 'undefined' || item2[col] === null
              ? ''
              : item2[col];
          let a =
            typeof value === 'number' ? value : String(value).toLowerCase();
          let b =
            typeof value2 === 'number' ? value2 : String(value2).toLowerCase();
          let abRes = a > b ? 1 * flip : b > a ? -1 * flip : 0;

          if (abRes === 0) {
            value = item.sort_create_datetime;
            value2 = item2.sort_create_datetime;
            a = typeof value === 'number' ? value : String(value).toLowerCase();
            b =
              typeof value2 === 'number'
                ? value2
                : String(value2).toLowerCase();
            abRes = a > b ? 1 * flip : b > a ? -1 * flip : 0;
          }

          if (abRes === 0) {
            value = item.member_no;
            value2 = item2.member_no;
            a = typeof value === 'number' ? value : String(value).toLowerCase();
            b =
              typeof value2 === 'number'
                ? value2
                : String(value2).toLowerCase();
            abRes = a > b ? 1 * flip : b > a ? -1 * flip : 0;
          }

          console.log(
            item.companyName,
            item2.companyName,
            a,
            b,
            item.sort_create_datetime,
            item2.sort_create_datetime,
            `abRes = ${abRes}`
          );

          return abRes;
        });

        return Promise.resolve(sorted_members);
      }
      return Promise.resolve(members);
    })
    .then(members => {
      return Base.uploadXlsxToS3(members, Define.XLSX.EXPORT.MEMBER);
    })
    .then(result => {
      const data = {
        url: result,
      };
      return Base.createSuccessResponse(cb, data);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
