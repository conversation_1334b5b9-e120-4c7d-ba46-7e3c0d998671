const Define = require(process.env.COMMON_LAYER_PATH + 'define');
const Validator = require(process.env.COMMON_LAYER_PATH + 'validator.js');
const Base = require(process.env.COMMON_LAYER_PATH + 'base.js');
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js');
const pool = new PgPool();

exports.handle = function (e, ctx, cb) {
  const params = Base.parseRequestBody(e.body);
  ctx.callbackWaitsForEmptyEventLoop = false;
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => Validator.validation(params))
    .then(() => {
      const sqlParams = [
        Base.extractTenantId(e),
        params.exhibition_no,
        params.lot_no,
        Base.extractAdminNo(e),
      ];
      return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.CANCEL_EXHIBITION_ITEM, sqlParams);
    })
    .then(result => Base.createSuccessResponse(cb, result))
    .catch(error => {
      console.log('error: ' + JSON.stringify(error));
      // Check sql response
      if (error && error.status === 400) {
        if (error.message === 'EXHIBITION_ENDED_EXCEPTION') {
          const err = {exhibition_status: 'E000607'};
          return Base.createErrorResponse(
            cb,
            Validator.createErrorResponse(err)
          );
        }
      }
      return Base.createErrorResponse(cb, error);
    });
};
