const async = require('async');
const {DeleteObjectCommand, S3Client} = require('@aws-sdk/client-s3');
const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool();

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);

  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log('delete-notice');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => Validator.validation(params))
    .then(() => {
      // Get file list
      const notice_no = params.notice_no;
      return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_NOTICE_FILES_FUNCTION, [notice_no]);
    })
    .then(fileList => {
      // Delete physical files
      console.log(`fileList: ${JSON.stringify(fileList)}`);
      if (
        typeof fileList === 'undefined' ||
        fileList === null ||
        fileList.length <= 0
      ) {
        console.log('fileList NULL');
        return Promise.resolve();
      }
      const deleteFiles = [];
      for (let i = 0; i < fileList.length; i++) {
        for (let j = 0; j < fileList[i].file.length; j++) {
          deleteFiles.push(fileList[i].file[j]);
        }
      }
      console.log('delete file from S3');
      const client = new S3Client({});
      // Delete copied files
      return Promise.all(
        deleteFiles.map(file => {
          const command = new DeleteObjectCommand({
            Bucket: process.env.S3_BUCKET,
            Key: file,
          });
          return client.send(command);
        })
      )
        .then(res => {
          console.log('delete file from S3 done', res);
          return Promise.resolve();
        })
        .catch(err => {
          console.log('delete file from S3 error', err);
          return Promise.resolve();
        });
    })
    .then(() => {
      // Update delete flag
      const notice_no = params.notice_no;
      const admin_no = Base.extractAdminNo(e);
      console.log(`admin_no = ${admin_no}`);

      let sql = '';
      let sql_params = null;

      // Update dalete flag
      sql = Define.QUERY.DELETE_NOTICE_FUNCTION;
      sql_params = [notice_no, admin_no];

      console.log(`sql = ${JSON.stringify(sql)}`);
      console.log(`sql_params = ${JSON.stringify(sql_params)}`);

      return pool.rlsQuery(Base.extractTenantId(e),sql, sql_params);
    })
    .then(notice_no => {
      const response = {
        data: notice_no,
      };
      return Base.createSuccessResponse(cb, response);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
