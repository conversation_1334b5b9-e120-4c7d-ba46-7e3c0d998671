const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  ctx.callbackWaitsForEmptyEventLoop = false;
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      if (
        params.startDateTimeFlag &&
        params.endDateTimeFlag &&
        params.startDateTime &&
        params.endDateTime
      ) {
        params.compareDate =
          Date.parse(`${params.endDateTime}`) -
          Date.parse(`${params.startDateTime}`);
      }
      if (params.startDateTime) {
        params.startDate = `${params.startDateTime} 00:00:00`;
      }
      if (params.endDateTime) {
        params.endDate = `${params.endDateTime} 23:59:59`;
      }
      if (params.toAddress) {
        params.toAddressQuerry = params.toAddress;
      }
      if (params.subject) {
        params.subjectQuerry = params.subject;
      }
      params.startDateTimeVali = params.startDateTimeFlag ? 1 : -1;
      params.endDateTimeVali = params.endDateTimeFlag ? 1 : -1;
      return Validator.validation(params);
    })
    .then(() => {
      console.log('GET COUNT');
      const sqlParams = [Base.extractTenantId(e), null, null, null, null];
      return pool.rlsQuery(Base.extractTenantId(e),
        Define.QUERY.GET_EMAIL_NOTIFICATION_LIST_COUNT,
        sqlParams
      );
    })
    .then(data => {
      console.log('SEARCH');

      let total_count = 0;
      if (data && data.length > 0) {
        total_count = data[0].count || 0;
      }

      if (total_count === 0) {
        return Promise.resolve({
          data: [],
          current_count: 0,
          total_count: 0,
        });
      }

      const sqlParams = [
        Base.extractTenantId(e),
        params.toAddressQuerry,
        params.subjectQuerry,
        params.startDate,
        params.endDate,
      ];
      return pool
        .rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_EMAIL_NOTIFICATION_LIST_FUNCTION, sqlParams)
        .then(emails => {
          const ret = {
            data: emails,
            current_count: emails ? emails.length : 0,
            total_count,
          };
          return Promise.resolve(ret);
        });
    })
    .then(res => {
      return Base.createSuccessResponse(cb, res);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
