const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);
  console.log('export-items-xlsx-file');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => Validator.validation(params))
    .then(() => {
      // Get constant
      console.log('GET CONSTANT');

      const sqlParams = [
        ['OPTION_KAHI', 'PRODUCT_CATEGORY'],
        Base.extractTenantId(e),
        Base.extractAdminLanguageCode(e),
      ];

      return pool
        .rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_CONSTANTS_BY_KEYS, sqlParams)
        .then(data => {
          const OPTION_CATEGORY = {};
          const OPTION_KAHI = {};
          const OPTION_UMU = {};

          for (let i = 0; i < data.length; i++) {
            switch (data[i].key_string) {
              case 'OPTION_KAHI':
                OPTION_KAHI[data[i].value1] = data[i].value2;
                break;
              case 'PRODUCT_CATEGORY':
                OPTION_CATEGORY[data[i].value1] = data[i].value2;
                break;
              default:
                console.log('dafault');
            }
          }

          // 有無 flag
          OPTION_UMU['0'] = '';
          OPTION_UMU['1'] = '有';

          constants = {
            OPTION_CATEGORY,
            OPTION_KAHI,
            OPTION_UMU,
          };
          return Promise.resolve();
        });
    })
    .then(() => {
      console.log('GET ITEMS XLSX');
      const sqlParams = [
        params.exhibition_no,
        Base.extractTenantId(e),
        null,
        null,
        Base.extractAdminLanguageCode(e),
      ];
      const sql = Define.QUERY.GET_ITEMS_XLSX_FUNCTION;
      console.log(`sql: ${sql}`);
      return pool.rlsQuery(Base.extractTenantId(e),sql, sqlParams);
    })
    .then(lots => {
      console.log('FILL IN DATA');
      lots.map(row => {
        if (row.localized_json_array) {
          const jpField =
            row.localized_json_array.find(item => item.f1 === 'ja')?.f2 || {};
          const enField =
            row.localized_json_array.find(item => item.f1 === 'en')?.f2 || {};

          // Convert null to ''
          Object.keys(jpField).map(f2_key => {
            row[f2_key] = Base.nullToString(jpField[f2_key]);
          });
          // English note1_en, note2_en
          row.note1_en = Base.nullToString(enField.note1);
          row.note2_en = Base.nullToString(enField.note2);
          // 最低入札数量
          row.lowest_bid_quantity = row.lowest_bid_quantity || 0;
          row.lowest_bid_price = row.lowest_bid_price || 0;
          // 最低落札数量
          row.lowest_bid_accept_quantity = row.lowest_bid_accept_quantity || 0;
          row.lowest_bid_accept_price = row.lowest_bid_accept_price || 0;
          // Category
          row.category = constants.OPTION_CATEGORY[row.category] || '';
        }
      });
      return Promise.resolve(lots);
    })
    .then(lots => {
      return Base.uploadXlsxToS3(lots, Define.XLSX.EXPORT.EXHIBITION);
    })
    .then(result => {
      const data = {
        url: result,
      };
      return Base.createSuccessResponse(cb, data);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
