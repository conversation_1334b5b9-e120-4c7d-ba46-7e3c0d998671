const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = function (e, ctx, cb) {
  const params = Base.parseRequestBody(e.body);
  ctx.callbackWaitsForEmptyEventLoop = false;
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => Validator.validation(params))
    .then(() => {
      const sqlParams = [
        Base.extractTenantId(e),
        Base.extractAdminLanguageCode(e),
        params.exhibitionNo,
      ];
      // 入札履歴情報を取得する
      return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_BID_HISTORY_FUNCTION, sqlParams);
    })
    .then(bidHistory => {
      return Promise.resolve().then(() => {
        bidHistory.map(bid => {
          // Price with comma
          bid.bid_price = Base.number2string(bid.bid_price);
          bid.bid_quantity = Base.number2string(bid.bid_quantity);
          bid.lowest_bid_price = Base.number2string(bid.lowest_bid_price);
          bid.after_current_price = Base.number2string(bid.after_current_price);
        });
        return Promise.resolve(bidHistory);
      });
    })
    .then(bidHistory => {
      return Base.uploadCsvToS3(
        bidHistory,
        Define.CSV.EXPORT.BID_HISTORY_CSV
      ).catch(error => {
        if (
          error &&
          error.status === 400 &&
          error.message === Define.MESSAGE.E000166
        ) {
          error.message = Define.MESSAGE.E000166;
        }
        return Promise.reject(error);
      });
    })
    .then(result => {
      const data = {
        url: result,
      };
      return Base.createSuccessResponse(cb, data);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
