/* eslint-disable camelcase */
const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);
const {GetObjectCommand, S3Client} = require('@aws-sdk/client-s3');
const {Upload} = require('@aws-sdk/lib-storage');
const {getSignedUrl} = require('@aws-sdk/s3-request-presigner');
const s3Zip = require('s3-zip');
const stream = require('node:stream');

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);
  console.log('export-inquiry-csv-file');

  const client = new S3Client({});
  const zipPrefix = 'お問い合わせ';
  const randomStr = `${Common.dateToStringWithDelimiter(new Date())}-${Base.randomString(10)}`;
  const files = [];

  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      // Validation
      console.log('VALIDATION');

      const validateResult = {};

      // Start date invalid
      const start_datetime = params.start_datetime;
      if (start_datetime !== '') {
        const tmpDate = new Date(start_datetime);
        console.log(`tmpDate = ${tmpDate}`);
        if (!(tmpDate instanceof Date && !isNaN(tmpDate))) {
          validateResult.start_datetime = 'E000502';
        }
      }

      // End date invalid
      const end_datetime = params.end_datetime;
      if (end_datetime !== '') {
        const tmpDate = new Date(end_datetime);
        console.log(`tmpDate = ${tmpDate}`);
        if (!(tmpDate instanceof Date && !isNaN(tmpDate))) {
          validateResult.end_datetime = 'E000502';
        }
      }

      // 開始日の大小チェック
      if (start_datetime !== '' && end_datetime !== '') {
        const tmpStDate = new Date(start_datetime);
        const tmpEnDate = new Date(end_datetime);
        if (tmpStDate.getTime() > tmpEnDate.getTime()) {
          console.log('開始日: From > To');
          validateResult.start_datetime = 'E000503';
        }
      }

      // Validation Fail
      if (Object.keys(validateResult).length > 0) {
        return Promise.reject(Validator.createErrorResponse(validateResult));
      }

      // Validation successful
      return Promise.resolve();
    })
    .then(() => {
      console.log('GET INQUIRIES FROM DB');

      const tenant_no = Base.extractTenantId(e);
      console.log(`tenant_no = ${tenant_no}`);

      const options = {year: 'numeric', month: '2-digit', day: '2-digit'};

      let startDateTime = params.start_datetime;
      if (startDateTime === '') {
        startDateTime = null;
      } else {
        const tmpDate = new Date(startDateTime);
        startDateTime = `${tmpDate.toLocaleDateString('ja-JP', options)} 00:00:00`;
      }
      let endDateTime = params.end_datetime;
      if (endDateTime === '') {
        endDateTime = null;
      } else {
        const tmpDate = new Date(endDateTime);
        endDateTime = `${tmpDate.toLocaleDateString('ja-JP', options)} 23:59:59`;
      }

      // Get inquiry list
      const sql = Define.QUERY.GET_INQUIRIES_CSV_FUNCTION;
      const sql_params = [tenant_no, startDateTime, endDateTime];

      console.log(`sql_params = ${JSON.stringify(sql_params)}`);
      console.log(`sql = ${JSON.stringify(sql)}`);

      return new Promise((resolve, reject) => {
        pool
          .rlsQuery(Base.extractTenantId(e),sql, sql_params)
          .then(inquiries => {
            // Check empty result
            if (
              typeof inquiries === 'undefined' ||
              inquiries === null ||
              inquiries.length === 0
            ) {
              const err = {
                status: 400,
                message: Define.MESSAGE.E000505,
              };
              return reject(err);
            }

            // Create 2 separate csv files
            const inquiry1 = [];
            const inquiry9 = [];

            for (const inquiry of inquiries) {
              if (
                typeof inquiry.file !== 'undefined' &&
                inquiry.file !== null &&
                inquiry.file.length > 0
              ) {
                for (const tmpF of inquiry.file) {
                  files.push({
                    file: tmpF,
                    zip: `${zipPrefix}/お問い合わせ${inquiry.inquiry_no}/${Base.getFilenameFromUrl(tmpF)}`,
                  });
                }
              }

              if (
                typeof inquiry.member_id === 'undefined' ||
                inquiry.member_id === null
              ) {
                inquiry.member_id = 'ゲスト';
              }

              if (
                typeof inquiry.free_field !== 'undefined' &&
                inquiry.free_field !== null
              ) {
                Object.assign(inquiry, inquiry.free_field);
                inquiry.free_field = null;
              }

              if (inquiry.classification === 1) {
                inquiry1.push(inquiry);
              }
              if (inquiry.classification === 9) {
                inquiry9.push(inquiry);
              }
            }
            return resolve({
              inquiry1,
              inquiry9,
            });
          })
          .catch(error => {
            return reject(error);
          });
      });
    })
    .then(({inquiry1, inquiry9}) => {
      console.log('CREATE XLSX FILES');
      return Promise.resolve()
        .then(() => {
          // Create 商品へのお問い合わせ.xlsx
          console.log(`inquiry1.length: ${inquiry1.length}`);
          if (inquiry1.length > 0) {
            const csvFileName1 = Common.format(
              Define.XLSX.EXPORT.INQUIRY_1.KEY_FORMAT,
              [randomStr]
            );
            return Base.uploadXlsxToS3(
              inquiry1,
              Define.XLSX.EXPORT.INQUIRY_1,
              csvFileName1
            ).then(csvUrl1 => {
              console.log(`csvUrl1: ${JSON.stringify(csvUrl1)}`);
              files.push({
                file: csvFileName1,
                zip: `${zipPrefix}/${Base.getFilenameFromUrl(csvFileName1)}`,
              });
            });
          }
          return Promise.resolve();
        })
        .then(() => {
          // Create お問い合わせ.xlsx
          console.log(`inquiry9.length: ${inquiry9.length}`);
          if (inquiry9.length > 0) {
            const csvFileName9 = Common.format(
              Define.XLSX.EXPORT.INQUIRY_9.KEY_FORMAT,
              [randomStr]
            );
            return Base.uploadXlsxToS3(
              inquiry9,
              Define.XLSX.EXPORT.INQUIRY_9,
              csvFileName9
            ).then(csvUrl9 => {
              console.log(`csvUrl: ${JSON.stringify(csvUrl9)}`);
              files.push({
                file: csvFileName9,
                zip: `${zipPrefix}/${Base.getFilenameFromUrl(csvFileName9)}`,
              });
            });
          }
          return Promise.resolve();
        })
        .then(() => {
          return Promise.resolve(files);
        })
        .catch(error => {
          return Promise.reject(error);
        });
    })
    .then(data => {
      console.log('ZIP FILES');
      const region = process.env.AWS_REGION;
      const bucket = process.env.S3_BUCKET;
      const folder = 'inquiry/';
      const zipFileName = `csv-download/${randomStr}/お問い合わせ.zip`;

      console.log(`region: ${region}`);
      console.log(`bucket: ${bucket}`);
      console.log(`folder: ${folder}`);
      console.log(`files: ${JSON.stringify(files)}`);

      if (
        typeof files === 'undefined' ||
        files === null ||
        files.length === 0
      ) {
        console.log('files empty!');
        const err = {
          status: 400,
          message: Define.MESSAGE.E000505,
        };
        return Promise.reject(err);
      }

      // Create body stream
      // https://stackoverflow.com/questions/37336050/pipe-a-stream-to-s3-upload
      try {
        const passThroughStream = new stream.PassThrough();
        const zipStream = s3Zip.archive(
          {region, bucket},
          null,
          files.map(fi => fi.file),
          files.map(fi => fi.zip)
        );
        const upload = new Upload({
          client,
          params: {
            Bucket: bucket,
            Key: zipFileName,
            Body: passThroughStream,
          },
        });
        zipStream.pipe(passThroughStream);
        return upload.done().then(data => {
          console.log('upload done', data);
          return Promise.resolve({
            Bucket: bucket,
            Key: zipFileName,
          });
        });
      } catch (err1) {
        const err = `catched error: ${err1}`;
        console.log(err);
        return Promise.reject(err1);
      }
    })
    .then(s3Result => {
      console.log('GET ZIP DOWNLOAD URL');

      if (typeof s3Result.Key === 'undefined' || s3Result.Key === null) {
        return Promise.reject();
      }
      const expiresIn = Number.parseInt(process.env.CSV_EXPIRES, 10);
      const command = new GetObjectCommand({
        Bucket: s3Result.Bucket,
        Key: s3Result.Key,
      });
      return getSignedUrl(client, command, {expiresIn});
    })
    .then(result => {
      console.log(`result: ${JSON.stringify(result)}`);
      const data = {
        url: result,
      };
      return Base.createSuccessResponse(cb, data);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
