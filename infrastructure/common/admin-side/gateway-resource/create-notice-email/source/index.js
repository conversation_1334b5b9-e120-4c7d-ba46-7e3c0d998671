const Define = require(process.env.COMMON_LAYER_PATH + 'define');
const Validator = require(process.env.COMMON_LAYER_PATH + 'validator.js');
const Base = require(process.env.COMMON_LAYER_PATH + 'base.js');
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js');
const pool = new PgPool();

exports.handle = function (e, ctx, cb) {
  const params = Base.parseRequestBody(e.body);
  console.log('params = ' + JSON.stringify(params));

  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log('create-notice-email');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      // Validation
      const validateResult = [];

      const notice = params['data'];
      if (typeof notice === 'undefined' || notice === null) {
        validateResult.push(Define.MESSAGE.E000705);
      }
      // Send date empty
      if (
        typeof notice.send_date === 'undefined' ||
        notice.send_date === null ||
        notice.send_date === ''
      ) {
        validateResult.push(Define.MESSAGE.E000706);
      }
      // Send time empty
      if (
        typeof notice.send_time === 'undefined' ||
        notice.send_time === null ||
        notice.send_time === ''
      ) {
        validateResult.push(Define.MESSAGE.E000722);
      }
      // Send datetime invalid
      tmpDate = new Date(notice.send_date + ' ' + notice.send_time);
      console.log('tmpDate = ' + tmpDate);
      if (tmpDate instanceof Date && !isNaN(tmpDate)) {
        // Compare to now
        const now = new Date(
          Date.now() + (new Date().getTimezoneOffset() + 9 * 60) * 60 * 1000
        );
        const compareWithNow = Date.parse(tmpDate) - Date.parse(now);
        if (compareWithNow < 0) {
          validateResult.push(Define.MESSAGE.E000718);
        }
      } else {
        validateResult.push(Define.MESSAGE.E000707);
      }

      // 日付が1年以内かのチェック
      const today = new Date(Date.now() + (new Date().getTimezoneOffset() + 9 * 60) * 60 * 1000);
      const maxDate = new Date(today);
      maxDate.setDate(today.getDate() + 364);
      maxDate.setHours(0, 0, 0, 0); // 時間をリセットして日付のみ取得
      console.log(`maxDate = ${maxDate}`)
      // 送信日のチェック
      const sendDate = new Date(`${notice.send_date}T00:00:00`);
      const sendDateDiff = maxDate - sendDate;
      if (sendDateDiff < 0) {
        validateResult.push(Define.MESSAGE.E000727);
      }

      // // Email priority
      // if (!Validator.checkRequired(notice.email_priority)) {
      //   validateResult.push(Define.MESSAGE.E000724)
      // }
      // if (!Validator.checkNaturalNumber(notice.email_priority)) {
      //   validateResult.push(Define.MESSAGE.E000725)
      // }

      // Get language name list
      const languages = notice.language_name;
      if (
        typeof languages === 'undefined' ||
        languages === null ||
        languages.length <= 0
      ) {
        validateResult.push(Define.MESSAGE.E000708);
      }

      // Title, Body check
      for (let i = 0; i < languages.length; i++) {
        if (!notice.title[i] || notice.title[i].trim().length === 0) {
          validateResult.push(
            Define.MESSAGE.E000723.replace(
              '#',
              (languages.length > 1 ? languages[i] + 'の' : '') + 'タイトル'
            )
          );
        }
        if (notice.title[i] && notice.title[i].trim().length > 78) {
          validateResult.push(
            Define.MESSAGE.E000719.replace(
              '#',
              languages.length > 1 ? languages[i] : ''
            )
          );
        }
        if (!notice.body[i] || notice.body[i].trim().length === 0) {
          validateResult.push(
            Define.MESSAGE.E000723.replace(
              '#',
              (languages.length > 1 ? languages[i] + 'の' : '') + '本文'
            )
          );
        }
      }

      // Validation Fail
      if (validateResult.length > 0) {
        const error = {
          status: 400,
          message: validateResult,
        };
        return Promise.reject(error);
      }

      // Validation successful
      if (params['validation_mode'] === true) {
        const response = {
          status: 200,
          message: '',
        };
        return Promise.reject(response);
      } else {
        return Promise.resolve();
      }
    })
    .then(() => {
      // Get notice_email_no
      const admin_no = Base.extractAdminNo(e);
      console.log('admin_no = ' + admin_no);
      const tenant_no = Base.extractTenantId(e);
      console.log('tenant_no = ' + tenant_no);

      const notice = params['data'];
      let notice_email_no = 0;
      if (
        typeof notice.notice_email_no !== 'undefined' &&
        notice.notice_email_no !== null
      ) {
        notice_email_no = notice.notice_email_no;
      }

      // Get language list
      const languages = notice.language_code;
      // Start insert data
      return new Promise((resolve, reject) => {
        insertAllNoticeEmail(
          0,
          languages,
          notice,
          notice_email_no,
          admin_no,
          tenant_no,
          pool,
          res_notice_email_no => {
            if (res_notice_email_no === 0) {
              // Failed
              const response = {
                status: 400,
                message: Define.MESSAGE.E000710,
              };
              console.log('error', JSON.stringify(response));
              return reject(response);
            }
            // Successful
            return resolve(res_notice_email_no);
          }
        );
      });
    })
    .then(noticeEmail => {
      const response = {
        data: noticeEmail,
      };

      return Base.createSuccessResponse(cb, response);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};

const insertAllNoticeEmail = function (
  count,
  languages,
  notice,
  res_notice_email_no,
  admin_no,
  tenant_no,
  pool,
  callback
) {
  // Check max length
  if (count >= languages.length) {
    callback(res_notice_email_no);
    return;
  }

  // Res_notice_email_no=0 → DB update failed
  // When count=0 skip
  if (count > 0 && res_notice_email_no === 0) {
    callback(res_notice_email_no);
    return;
  }

  const send_datetime = notice.send_date + ' ' + notice.send_time;

  // Insert notice
  const sql = Define.QUERY.INSERT_OR_UPDATE_NOTICE_EMAIL_FUNCTION;
  const sql_params = [
    tenant_no,
    res_notice_email_no,
    send_datetime,
    0,
    languages[count],
    notice.title[count],
    '',
    '',
    notice.body[count],
    null,
    notice.file[count],
    admin_no,
    admin_no,
  ];

  console.log('sql = ' + JSON.stringify(sql));
  console.log('sql_params = ' + JSON.stringify(sql_params));

  pool
    .rlsQuery(tenant_no,sql, sql_params)
    .then(result => {
      console.log('result = ', JSON.stringify(result));
      // [{"f_insert_or_update_notice":215}]
      let retId = result[0]['f_insert_or_update_notice_email'];
      if (typeof retId === 'undefined' || retId === null) {
        retId = 0;
      }
      return insertAllNoticeEmail(
        count + 1,
        languages,
        notice,
        retId,
        admin_no,
        tenant_no,
        pool,
        callback
      );
    })
    .catch(error => {
      console.log('error', error);
      return insertAllNoticeEmail(
        count + 1,
        languages,
        notice,
        0,
        admin_no,
        tenant_no,
        pool,
        callback
      );
    });
};
