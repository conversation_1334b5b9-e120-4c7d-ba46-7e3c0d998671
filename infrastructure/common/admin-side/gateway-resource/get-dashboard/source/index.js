const Define = require(process.env.COMMON_LAYER_PATH + 'define');
const Validator = require(process.env.COMMON_LAYER_PATH + 'validator.js');
const Base = require(process.env.COMMON_LAYER_PATH + 'base.js');
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js');
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = function (e, ctx, cb) {
  console.log('get-dashboard');
  const params = Base.parseRequestBody(e.body);
  console.log('params = ' + JSON.stringify(params));
  console.log('e = ' + JSON.stringify(e));
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      // Get tenant_no
      const tenant_no = Base.extractTenantId(e);
      const language = Base.extractAdminLanguageCode(e);
      console.log('tenant_no = ' + tenant_no);

      // Get member list
      const sql = Define.QUERY.GET_DASHBOARD_FUNCTION;
      const sql_params = [
        tenant_no,
        language,
        params['exhibition_name'],
        params['category_id'],
      ];

      console.log('sql_params = ' + JSON.stringify(sql_params));
      console.log('sql = ' + JSON.stringify(sql));

      return pool.rlsQuery(Base.extractTenantId(e),sql, sql_params);
    })
    .then(data => {
      console.log('data = ' + JSON.stringify(data));
      return Base.createSuccessResponse(cb, data);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
