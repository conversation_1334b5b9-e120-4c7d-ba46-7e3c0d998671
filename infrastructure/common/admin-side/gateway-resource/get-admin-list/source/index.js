const Define = require(process.env.COMMON_LAYER_PATH + 'define');
const Validator = require(process.env.COMMON_LAYER_PATH + 'validator.js');
const Base = require(process.env.COMMON_LAYER_PATH + 'base.js');
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js');
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = function (e, ctx, cb) {
  const params = Base.parseRequestBody(e.body);
  console.log('params = ' + JSON.stringify(params));

  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log('get-admin-list');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    // .then(() => Validator.validation(params))
    .then(() => {
      // Get tenant_no
      const tenant_no = Base.extractTenantId(e);
      console.log('tenant_no = ' + tenant_no);
      // Get admin list
      const sql = Define.QUERY.GET_ADMIN_LIST_FUNCTION;
      console.log('sql = ' + JSON.stringify(sql));
      return pool.rlsQuery(Base.extractTenantId(e),sql, [tenant_no]);
    })
    .then(admins => {
      console.log('admins = ' + JSON.stringify(admins));
      return Base.createSuccessResponse(cb, admins);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
