const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const {
  AdminUpdateUserAttributesCommand,
  AdminSetUserPasswordCommand,
  AdminEnableUserCommand,
  AdminDisableUserCommand,
  CognitoIdentityProviderClient,
} = require('@aws-sdk/client-cognito-identity-provider');

const pool = new PgPool();
const client = new CognitoIdentityProviderClient({});
const {isBlank, isNullish} = Common;
/**
 * Extracts user information from the Cognito claims.
 */
function getUserFromClaims(e) {
  return {
    login_admin_no: Base.extractAdminNo(e),
    tenant_no: Base.extractTenantId(e),
    role_id: Base.extractRoleId(e),
    admin_name: Base.extractAdminName(e),
  };
}
function validateInput(paramData, isSelfEdit) {
  // Validation
  const errors = {};

  if (isBlank(paramData.login_id)) errors.login_id = 'E000124'; // ログインIDは必須です。
  if (isBlank(paramData.admin_name)) errors.admin_name = 'E000130'; // 氏名は必須です。
  if (isNullish(paramData.delete_flag)) errors.delete_flag = 'E000127'; // 状態は必須です。

  // 編集の時に、パスワードを入力すれば確認を行う
  const password = paramData.password;
  if (!isBlank(password)) {
    // Check pattern matching
    const pattern =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[a-zA-Z0-9$@$!%*#?&_-]{8,}$/;
    if (!pattern.test(password)) errors.password = 'E000135';
    // Check confirm password
    const password_confirm = paramData.password_confirm;
    if (!isBlank(password_confirm)) {
      if (password !== password_confirm) errors.password_confirm = 'E000129';
    } else {
      errors.password_confirm = 'E000128';
    }
  }
  // パスワード（確認）が入力されているが、パスワードが入力されていない場合はエラー
  if (!isBlank(paramData.password_confirm) && isBlank(paramData.password)) {
    errors.password = 'E000129'; // パスワードとパスワード(確認)が一致しません。
  }
  // ログイン者ではない場合は必須項目チェック
  if (!isSelfEdit) {
    // 権限
    const role_id = paramData.role_id;
    if (isBlank(role_id)) errors.role_id = 'E000126'; // 権限は必須です。

    if (isNullish(paramData.delete_flag)) errors.delete_flag = 'E000127'; // 状態は必須です。
  }
  // Validation Fail
  if (Object.keys(errors).length > 0) {
    const error = Validator.createErrorResponse(errors);
    throw {
      status: 406,
      name: 'Error by Validator.validation',
      message: error.errors,
    };
  }
}

async function updateAdminInDb(
  paramData,
  hash_password,
  loggedInUser,
  isSelfEdit
) {
  const sql = Define.QUERY.INSERT_OR_UPDATE_ADMIN_FUNCTION;
  const tenantId = loggedInUser.tenant_no;
  let sql_params;

  // ログイン者自分自身は氏名のみ編集可能
  if (isSelfEdit) {
    sql_params = [
      tenantId,
      paramData.admin_name,
      paramData.login_id,
      hash_password,
      null, // role_id not updatable
      loggedInUser.login_admin_no, // in_create_admin_no
      loggedInUser.login_admin_no, // in_update_admin_no
      null, // in_delete_flag not updatable
    ];
  } else {
    sql_params = [
      tenantId,
      paramData.admin_name,
      paramData.login_id,
      hash_password,
      paramData.role_id,
      loggedInUser.login_admin_no,
      loggedInUser.login_admin_no,
      paramData.delete_flag,
    ];
  }

  return pool.rlsQuery(tenantId, sql, sql_params);
}

/**
 * Updates user attributes in Cognito.
 */
async function updateCognitoAttributes(paramData) {
  const attributes = [
    {Name: 'custom:admin_name', Value: paramData.admin_name},
    {Name: 'custom:role_id', Value: String(paramData.role_id)},
  ];

  const command = new AdminUpdateUserAttributesCommand({
    UserAttributes: attributes,
    UserPoolId: process.env.COGNITO_USER_POOL_ID,
    Username: paramData.login_id, // メールアドレス
  });

  await client.send(command);
  console.log(
    `Successfully updated Cognito attributes for ${paramData.login_id}`
  );
}

/**
 * Sets a new permanent password for the user in Cognito.
 */
async function updateCognitoPassword(username, password) {
  const command = new AdminSetUserPasswordCommand({
    Password: password,
    Permanent: true,
    Username: username,
    UserPoolId: process.env.COGNITO_USER_POOL_ID,
  });

  await client.send(command);
  console.log(
    `Successfully set new permanent password for ${username} in Cognito.`
  );
}

/**
 * Enables or disables a user in Cognito based on delete_flag.
 * delete_flag: 0 = active (enable user), 1 = disabled (disable user)
 */
async function updateCognitoUserStatus(username, delete_flag) {
  const isActive = delete_flag === 0 || delete_flag === '0';

  if (isActive) {
    // Enable user login
    const command = new AdminEnableUserCommand({
      UserPoolId: process.env.COGNITO_USER_POOL_ID,
      Username: username,
    });
    await client.send(command);
    console.log(`Successfully enabled user ${username} in Cognito.`);
  } else {
    // Disable user login
    const command = new AdminDisableUserCommand({
      UserPoolId: process.env.COGNITO_USER_POOL_ID,
      Username: username,
    });
    await client.send(command);
    console.log(`Successfully disabled user ${username} in Cognito.`);
  }
}

// --- Main Handler ---
async function editAdmin(e) {
  await Base.startRequest(e);
  await Base.checkAccessIpAddress(
    pool,
    e.requestContext.identity.sourceIp,
    Base.extractTenantId(e)
  );

  const params = Base.parseRequestBody(e.body);
  const paramData = params.data;

  const loggedInUser = getUserFromClaims(e);
  console.log('Logged-in user:', loggedInUser);

  // Check Self-Editing Condition
  const isSelfEdit = loggedInUser.login_admin_no === paramData.admin_no;

  validateInput(paramData, isSelfEdit);

  // Update Cognito Password if provided
  let hash_password = null;
  if (!isBlank(paramData.password)) {
    hash_password = await Base.hashPassword(paramData.password);
    await updateCognitoPassword(paramData.login_id, paramData.password);
  }

  // Update Database
  const result = await updateAdminInDb(
    paramData,
    hash_password,
    loggedInUser,
    isSelfEdit
  );
  console.log('[🔴 TRACE LOG] edit-admin/index.js : ', result);

  if (!result) {
    throw {
      status: 500,
      name: 'Database Error',
      message: 'Failed to update admin in database.',
    };
  }

  // Update Cognito Custom Attributes
  await updateCognitoAttributes(paramData);

  // Update Cognito User Status (enable/disable) based on delete_flag
  // Only update if not self-editing and delete_flag is provided
  if (!isNullish(paramData.delete_flag)) {
    await updateCognitoUserStatus(paramData.login_id, paramData.delete_flag);
  }

  return {admin_no: result.admin_no};
}

exports.handle = (e, ctx, cb) => {
  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log('🚀 edit-admin Lambda started , requested event: ', e);

  editAdmin(e)
    .then(result => {
      console.log('✅ edit-admin completed successfully');
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => {
      console.error('❌ Error in edit-admin handler:', error);

      const statusCode = error.status || 500;
      const response = {
        status: statusCode,
        name: error.name || 'InternalServerError',
        message: error.message || Define.MESSAGE.E900001,
      };
      Base.createErrorResponse(cb, response);
    });
};
