const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = function (e, ctx, cb) {
  const params = Base.parseRequestBody(e.body);
  ctx.callbackWaitsForEmptyEventLoop = false;
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => Validator.validation(params))
    .then(() => {
      const sqlParams = [
        Base.extractTenantId(e),
        Base.extractAdminLanguageCode(e),
        params.exhibitionNo,
      ];
      // 入札順位情報を取得する
      return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_BID_ORDER_FUNCTION, sqlParams);
    })
    .then(result => {
      const bidOrder = [];
      for (item of result) {
        const order = {};
        order.product_id = item.product_id || '';
        order.product_name = item.product_name || '';
        order.quantity = Base.number2string(item.quantity);
        order.lowest_bid_price = Base.number2string(item.lowest_bid_price);
        order.lowest_bid_quantity = Base.number2string(
          item.lowest_bid_quantity
        );
        order.lowest_bid_accept_price = Base.number2string(
          item.lowest_bid_accept_price
        );
        order.lowest_bid_accept_quantity = Base.number2string(
          item.lowest_bid_accept_quantity
        );
        // order.current_price = Base.number2string(item.current_price)

        // Member name and bid information
        for (let index = 0; index < item.ranking_list.length; index++) {
          order[`member_id_${index + 1}`] = item.ranking_list[index].member_id;
          order[`customer_code_${index + 1}`] =
            item.ranking_list[index].customer_code;
          order[`company_name_${index + 1}`] =
            item.ranking_list[index].company_name;
          order[`bid_price_${index + 1}`] = Base.number2string(
            item.ranking_list[index].bid_price
          );
          order[`bid_quantity_${index + 1}`] = Base.number2string(
            item.ranking_list[index].bid_quantity
          );
        }
        bidOrder.push(order);
      }

      // Sort alphabetically by product name
      // bidOrder = bidOrder.sort((x, y) => {
      //   return x.product_name > y.product_name ? 1 : -1
      // })

      return Base.uploadCsvToS3(
        bidOrder,
        Define.CSV.EXPORT.BID_ORDER_CSV
      ).catch(error => {
        if (
          error &&
          error.status === 400 &&
          error.message === Define.MESSAGE.E000166
        ) {
          error.message = Define.MESSAGE.E000166;
        }
        return Promise.reject(error);
      });
    })
    .then(result => {
      const data = {
        url: result,
      };
      return Base.createSuccessResponse(cb, data);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
